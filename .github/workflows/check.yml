name: Check

on:
  push:
    branches:
      - master
  pull_request:
    branches:
      - master
  workflow_dispatch:

jobs:
  TyposCheck:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: crate-ci/typos@v1.22.7
        with:
          config: ./typos.toml
          
  UnitTest:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Setup Go
      uses: actions/setup-go@v3
      with:
        go-version-file: './v2/go.mod'
        cache: true
        cache-dependency-path: './v2/go.sum'
    - name: Format
      run: ./check.sh format
    - name: Vet
      run: ./check.sh vet
    - name: UnitTest
      run: ./check.sh unittest
    # - name: IntegrationTest
    #   run: ./check.sh integration
