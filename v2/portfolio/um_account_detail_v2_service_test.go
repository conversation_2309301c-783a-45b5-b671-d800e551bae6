package portfolio

import (
	"testing"

	"github.com/stretchr/testify/suite"
)

type umAccountDetailV2ServiceTestSuite struct {
	baseTestSuite
}

func TestUMAccountDetailV2Service(t *testing.T) {
	suite.Run(t, new(umAccountDetailV2ServiceTestSuite))
}

func (s *umAccountDetailV2ServiceTestSuite) TestGetUMAccountDetailV2() {
	data := []byte(`{
		"assets": [
			{
				"asset": "USDT",
				"crossWalletBalance": "23.********",
				"crossUnPnl": "0.********",
				"maintMargin": "0.********",
				"initialMargin": "0.********",
				"positionInitialMargin": "0.********",
				"openOrderInitialMargin": "0.********",
				"updateTime": *************
			}
		],
		"positions": [
			{
				"symbol": "BTCUSDT",
				"initialMargin": "0",
				"maintMargin": "0",
				"unrealizedProfit": "0.********",
				"positionSide": "BOTH",
				"positionAmt": "0",
				"updateTime": 0,
				"notional": "86.********"
			}
		]
	}`)

	s.mockDo(data, nil)
	defer s.assertDo()

	s.assertReq(func(r *request) {
		e := newSignedRequest()
		s.assertRequestEqual(e, r)
	})

	res, err := s.client.NewGetUMAccountDetailV2Service().Do(newContext())
	s.r().NoError(err)

	// Validate assets
	s.r().Len(res.Assets, 1)
	s.r().Equal("USDT", res.Assets[0].Asset)
	s.r().Equal("23.********", res.Assets[0].CrossWalletBalance)
	s.r().Equal(int64(*************), res.Assets[0].UpdateTime)

	// Validate positions
	s.r().Len(res.Positions, 1)
	s.r().Equal("BTCUSDT", res.Positions[0].Symbol)
	s.r().Equal("BOTH", res.Positions[0].PositionSide)
	s.r().Equal("86.********", res.Positions[0].Notional)
}
