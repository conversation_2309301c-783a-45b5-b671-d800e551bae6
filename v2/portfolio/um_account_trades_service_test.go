package portfolio

import (
	"testing"

	"github.com/stretchr/testify/suite"
)

type umAccountTradesServiceTestSuite struct {
	baseTestSuite
}

func TestUMAccountTradesService(t *testing.T) {
	suite.Run(t, new(umAccountTradesServiceTestSuite))
}

func (s *umAccountTradesServiceTestSuite) TestGetTrades() {
	data := []byte(`[
		{
			"symbol": "BTCUSDT",
			"id": ********,
			"orderId": *********,
			"side": "SELL",
			"price": "28511.00",
			"qty": "0.010",
			"realizedPnl": "2.********",
			"quoteQty": "285.11000",
			"commission": "-0.********",
			"commissionAsset": "USDT",
			"time": *************,
			"buyer": false,
			"maker": false,
			"positionSide": "BOTH"
		}
	]`)
	s.mockDo(data, nil)
	defer s.assertDo()

	symbol := "BTCUSDT"
	limit := 500
	fromID := int64(********)
	startTime := int64(*************)
	endTime := int64(*************)
	s.assertReq(func(r *request) {
		e := newSignedRequest().setParams(params{
			"symbol":    symbol,
			"limit":     limit,
			"fromId":    fromID,
			"startTime": startTime,
			"endTime":   endTime,
		})
		s.assertRequestEqual(e, r)
	})

	trades, err := s.client.NewUMAccountTradesService().
		Symbol(symbol).
		Limit(limit).
		FromID(fromID).
		StartTime(startTime).
		EndTime(endTime).
		Do(newContext())
	s.r().NoError(err)
	s.r().Len(trades, 1)
	s.r().Equal("BTCUSDT", trades[0].Symbol)
	s.r().Equal(int64(********), trades[0].ID)
	s.r().Equal(int64(*********), trades[0].OrderID)
	s.r().Equal("SELL", trades[0].Side)
	s.r().Equal("28511.00", trades[0].Price)
	s.r().Equal("BOTH", trades[0].PositionSide)
}
