package portfolio

import (
	"testing"

	"github.com/stretchr/testify/suite"
)

type accountServiceTestSuite struct {
	baseTestSuite
}

func TestAccountService(t *testing.T) {
	suite.Run(t, new(accountServiceTestSuite))
}

func (s *accountServiceTestSuite) TestGetAccount() {
	data := []byte(`{
		"uniMMR": "5167.********",
		"accountEquity": "122607.********",
		"actualEquity": "73.********",
		"accountInitialMargin": "23.********",
		"accountMaintMargin": "23.********",
		"accountStatus": "NORMAL",
		"virtualMaxWithdrawAmount": "1627523.********",
		"totalAvailableBalance": "100.00",
		"totalMarginOpenLoss": "0.00",
		"updateTime": *************
	}`)
	s.mockDo(data, nil)
	defer s.assertDo()
	s.assertReq(func(r *request) {
		e := newSignedRequest()
		s.assertRequestEqual(e, r)
	})

	res, err := s.client.NewGetAccountService().Do(newContext())
	s.r().NoError(err)
	s.assertAccountEqual(res, &Account{
		UniMMR:                   "5167.********",
		AccountEquity:            "122607.********",
		ActualEquity:             "73.********",
		AccountInitialMargin:     "23.********",
		AccountMaintMargin:       "23.********",
		AccountStatus:            "NORMAL",
		VirtualMaxWithdrawAmount: "1627523.********",
		TotalAvailableBalance:    "100.00",
		TotalMarginOpenLoss:      "0.00",
		UpdateTime:               *************,
	})
}

func (s *accountServiceTestSuite) assertAccountEqual(a, e *Account) {
	r := s.r()
	r.Equal(e.UniMMR, a.UniMMR, "UniMMR")
	r.Equal(e.AccountEquity, a.AccountEquity, "AccountEquity")
	r.Equal(e.ActualEquity, a.ActualEquity, "ActualEquity")
	r.Equal(e.AccountInitialMargin, a.AccountInitialMargin, "AccountInitialMargin")
	r.Equal(e.AccountMaintMargin, a.AccountMaintMargin, "AccountMaintMargin")
	r.Equal(e.AccountStatus, a.AccountStatus, "AccountStatus")
	r.Equal(e.VirtualMaxWithdrawAmount, a.VirtualMaxWithdrawAmount, "VirtualMaxWithdrawAmount")
	r.Equal(e.TotalAvailableBalance, a.TotalAvailableBalance, "TotalAvailableBalance")
	r.Equal(e.TotalMarginOpenLoss, a.TotalMarginOpenLoss, "TotalMarginOpenLoss")
	r.Equal(e.UpdateTime, a.UpdateTime, "UpdateTime")
}
