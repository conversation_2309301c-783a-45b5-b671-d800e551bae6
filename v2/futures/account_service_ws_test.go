package futures

import (
	"testing"

	"github.com/adshao/go-binance/v2/common"
	"github.com/adshao/go-binance/v2/common/websocket/mock"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
)

type accountWsServiceTestSuite struct {
	suite.Suite
	apiKey     string
	secretKey  string
	mockClient *mock.MockClient
	mockCtrl   *gomock.Controller
}

func TestAccountWsService(t *testing.T) {
	suite.Run(t, new(accountWsServiceTestSuite))
}

func (s *accountWsServiceTestSuite) SetupTest() {
	s.apiKey = "dummyAPIKey"
	s.secretKey = "dummySecretKey"
	s.mockCtrl = gomock.NewController(s.T())
	s.mockClient = mock.NewMockClient(s.mockCtrl)
}

func (s *accountWsServiceTestSuite) TearDownTest() {
	s.mockCtrl.Finish()
}

func (s *accountWsServiceTestSuite) TestGetAccountInfo() {
	data := []byte(`{
  "id": "873a969f-2d36-472a-ace5-0a61c5fc7d39",
  "status": 200,
  "result": {
    "totalInitialMargin": "13.********",
    "totalMaintMargin": "0.********",
    "totalWalletBalance": "192.********",
    "totalUnrealizedProfit": "6.********",
    "totalMarginBalance": "199.********",
    "totalPositionInitialMargin": "13.********",
    "totalOpenOrderInitialMargin": "0.********",
    "totalCrossWalletBalance": "192.********",
    "totalCrossUnPnl": "6.********",
    "availableBalance": "185.********",
    "maxWithdrawAmount": "185.********",
    "assets": [
      {
        "asset": "USDT",
        "walletBalance": "192.********",
        "unrealizedProfit": "6.********",
        "marginBalance": "199.********",
        "maintMargin": "0.********",
        "initialMargin": "13.********",
        "positionInitialMargin": "13.********",
        "openOrderInitialMargin": "0.********",
        "crossWalletBalance": "192.********",
        "crossUnPnl": "6.********",
        "availableBalance": "185.********",
        "maxWithdrawAmount": "185.********",
        "updateTime": *************
      },
      {
        "asset": "USDC",
        "walletBalance": "0.********",
        "unrealizedProfit": "0.********",
        "marginBalance": "0.********",
        "maintMargin": "0.********",
        "initialMargin": "0.********",
        "positionInitialMargin": "0.********",
        "openOrderInitialMargin": "0.********",
        "crossWalletBalance": "0.********",
        "crossUnPnl": "0.********",
        "availableBalance": "0.********",
        "maxWithdrawAmount": "0.********",
        "updateTime": 0
      }
    ],
    "positions": [
      {
        "symbol": "SOLUSDT",
        "positionSide": "SHORT",
        "positionAmt": "-0.77",
        "unrealizedProfit": "6.********",
        "isolatedMargin": "0",
        "notional": "-136.********",
        "isolatedWallet": "0",
        "initialMargin": "13.********",
        "maintMargin": "0.********",
        "updateTime": *************
      }
    ]
  },
  "rateLimits": [
    {
      "rateLimitType": "REQUEST_WEIGHT",
      "interval": "MINUTE",
      "intervalNum": 1,
      "limit": 2400,
      "count": 50
    }
  ]
}`)

	requestID := "873a969f-2d36-472a-ace5-0a61c5fc7d39"
	s.mockClient.EXPECT().WriteSync(requestID, gomock.Any(), gomock.Any()).Return(data, nil)

	wsAccountV2Service := &WsAccountService{
		c:          s.mockClient,
		ApiKey:     s.apiKey,
		SecretKey:  s.secretKey,
		KeyType:    common.KeyTypeHmac,
		RecvWindow: 5000,
	}

	response, err := wsAccountV2Service.SyncGetAccountInfo(requestID)
	s.NoError(err)

	// verify
	s.Equal(200, response.Status)
	s.Equal(requestID, response.ID)

	// verify account info
	s.Equal("13.********", response.Result.TotalInitialMargin)
	s.Equal("192.********", response.Result.TotalWalletBalance)
	s.Equal("185.********", response.Result.AvailableBalance)

	// verify assets info
	s.Len(response.Result.Assets, 2)
	s.Equal("USDT", response.Result.Assets[0].Asset)
	s.Equal("192.********", response.Result.Assets[0].WalletBalance)

	// verify positions info
	s.Len(response.Result.Positions, 1)
	s.Equal("SOLUSDT", response.Result.Positions[0].Symbol)
	s.Equal("SHORT", response.Result.Positions[0].PositionSide)
	s.Equal("-0.77", response.Result.Positions[0].PositionAmt)
}

func (s *accountWsServiceTestSuite) TestGetAccountBalance() {
	data := []byte(`{
  "id": "7fe4c481-9784-4c02-8121-aacae6d2d38f",
  "status": 200,
  "result": [
    {
      "accountAlias": "SgsR",
      "asset": "USDT",
      "balance": "192.********",
      "crossWalletBalance": "192.********",
      "crossUnPnl": "6.********",
      "availableBalance": "185.********",
      "maxWithdrawAmount": "185.********",
      "marginAvailable": true,
      "updateTime": *************
    },
    {
      "accountAlias": "SgsR",
      "asset": "USDC",
      "balance": "0.********",
      "crossWalletBalance": "0.********",
      "crossUnPnl": "0.********",
      "availableBalance": "0.********",
      "maxWithdrawAmount": "0.********",
      "marginAvailable": true,
      "updateTime": 0
    }
  ],
  "rateLimits": [
    {
      "rateLimitType": "REQUEST_WEIGHT",
      "interval": "MINUTE",
      "intervalNum": 1,
      "limit": 2400,
      "count": 10
    }
  ]
}`)

	requestID := "7fe4c481-9784-4c02-8121-aacae6d2d38f"
	s.mockClient.EXPECT().WriteSync(requestID, gomock.Any(), gomock.Any()).Return(data, nil)

	wsAccountV2Service := &WsAccountService{
		c:          s.mockClient,
		ApiKey:     s.apiKey,
		SecretKey:  s.secretKey,
		KeyType:    common.KeyTypeHmac,
		RecvWindow: 5000,
	}

	response, err := wsAccountV2Service.SyncGetAccountBalance(requestID)
	s.NoError(err)

	// verify
	s.Equal(200, response.Status)
	s.Equal(requestID, response.ID)

	// verify balance info
	s.Len(response.Result, 2)
	s.Equal("USDT", response.Result[0].Asset)
	s.Equal("192.********", response.Result[0].Balance)
	s.Equal("185.********", response.Result[0].AvailableBalance)
}
