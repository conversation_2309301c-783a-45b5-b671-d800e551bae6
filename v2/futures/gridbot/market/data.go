package market

import (
	"context"
	"fmt"
	"math"
	"sync"
	"time"

	"github.com/adshao/go-binance/v2/futures"
	"github.com/adshao/go-binance/v2/futures/gridbot/config"
)

// MarketDataHandlerImpl implements the MarketDataHandlerInterface
type MarketDataHandlerImpl struct {
	config       *config.Config
	client       *futures.Client
	symbol       string
	
	// WebSocket connections
	connections  map[string]*WebSocketConnection
	
	// Data channels
	priceUpdates    chan *PriceUpdate
	depthUpdates    chan *DepthUpdate
	userUpdates     chan *UserDataUpdate
	eventChan       chan *MarketDataEvent
	
	// Current market state
	currentPrice    *PriceData
	currentDepth    *DepthData
	
	// Statistics
	statistics      *MarketDataStatistics
	
	// Synchronization
	mu              sync.RWMutex
	
	// State
	running         bool
	stopChan        chan struct{}
	
	// Price history for volatility calculation
	priceHistory    []float64
	maxHistorySize  int
}

// NewMarketDataHandler creates a new market data handler
func NewMarketDataHandler(client *futures.Client, cfg *config.Config, symbol string) *MarketDataHandlerImpl {
	return &MarketDataHandlerImpl{
		config:         cfg,
		client:         client,
		symbol:         symbol,
		connections:    make(map[string]*WebSocketConnection),
		priceUpdates:   make(chan *PriceUpdate, cfg.Advanced.WebSocket.BufferSize),
		depthUpdates:   make(chan *DepthUpdate, cfg.Advanced.WebSocket.BufferSize),
		userUpdates:    make(chan *UserDataUpdate, cfg.Advanced.WebSocket.BufferSize),
		eventChan:      make(chan *MarketDataEvent, cfg.Advanced.WebSocket.BufferSize),
		statistics:     &MarketDataStatistics{StartTime: time.Now()},
		stopChan:       make(chan struct{}),
		priceHistory:   make([]float64, 0),
		maxHistorySize: 100, // Keep last 100 price points for volatility calculation
		running:        false,
	}
}

// Start starts the market data handler
func (mdh *MarketDataHandlerImpl) Start() error {
	mdh.mu.Lock()
	defer mdh.mu.Unlock()
	
	if mdh.running {
		return fmt.Errorf("market data handler is already running")
	}
	
	mdh.running = true
	mdh.statistics.StartTime = time.Now()
	
	// Start background workers
	go mdh.statisticsWorker()
	go mdh.connectionMonitor()
	
	mdh.sendEvent(MarketDataEventType("handler_started"), nil, "Market data handler started")
	
	return nil
}

// Stop stops the market data handler
func (mdh *MarketDataHandlerImpl) Stop() error {
	mdh.mu.Lock()
	defer mdh.mu.Unlock()
	
	if !mdh.running {
		return nil
	}
	
	mdh.running = false
	
	// Stop all connections
	for name, conn := range mdh.connections {
		mdh.stopConnection(name, conn)
	}
	
	// Signal stop to background workers
	close(mdh.stopChan)
	
	mdh.sendEvent(MarketDataEventType("handler_stopped"), nil, "Market data handler stopped")
	
	return nil
}

// IsRunning returns whether the handler is running
func (mdh *MarketDataHandlerImpl) IsRunning() bool {
	mdh.mu.RLock()
	defer mdh.mu.RUnlock()
	return mdh.running
}

// SubscribePriceUpdates subscribes to price updates
func (mdh *MarketDataHandlerImpl) SubscribePriceUpdates() error {
	mdh.mu.Lock()
	defer mdh.mu.Unlock()
	
	if !mdh.running {
		return fmt.Errorf("handler is not running")
	}
	
	// Subscribe to mark price updates
	doneC, stopC, err := futures.WsMarkPriceServe(mdh.symbol, mdh.handleMarkPriceUpdate, mdh.handleError)
	if err != nil {
		return fmt.Errorf("failed to subscribe to mark price updates: %w", err)
	}
	
	conn := &WebSocketConnection{
		endpoint:   "markPrice",
		doneC:      doneC,
		stopC:      stopC,
		connected:  true,
		lastPing:   time.Now(),
	}
	
	mdh.connections["markPrice"] = conn
	mdh.statistics.TotalConnections++
	mdh.statistics.ActiveConnections++
	
	// Also subscribe to ticker updates for additional price data
	doneC2, stopC2, err := futures.WsMarketTickerServe(mdh.symbol, mdh.handleTickerUpdate, mdh.handleError)
	if err != nil {
		return fmt.Errorf("failed to subscribe to ticker updates: %w", err)
	}
	
	conn2 := &WebSocketConnection{
		endpoint:   "ticker",
		doneC:      doneC2,
		stopC:      stopC2,
		connected:  true,
		lastPing:   time.Now(),
	}
	
	mdh.connections["ticker"] = conn2
	mdh.statistics.TotalConnections++
	mdh.statistics.ActiveConnections++
	
	return nil
}

// SubscribeDepthUpdates subscribes to order book depth updates
func (mdh *MarketDataHandlerImpl) SubscribeDepthUpdates() error {
	mdh.mu.Lock()
	defer mdh.mu.Unlock()
	
	if !mdh.running {
		return fmt.Errorf("handler is not running")
	}
	
	// Subscribe to partial depth updates (20 levels)
	doneC, stopC, err := futures.WsPartialDepthServe(mdh.symbol, 20, mdh.handleDepthUpdate, mdh.handleError)
	if err != nil {
		return fmt.Errorf("failed to subscribe to depth updates: %w", err)
	}
	
	conn := &WebSocketConnection{
		endpoint:   "depth",
		doneC:      doneC,
		stopC:      stopC,
		connected:  true,
		lastPing:   time.Now(),
	}
	
	mdh.connections["depth"] = conn
	mdh.statistics.TotalConnections++
	mdh.statistics.ActiveConnections++
	
	return nil
}

// SubscribeUserDataUpdates subscribes to user data updates
func (mdh *MarketDataHandlerImpl) SubscribeUserDataUpdates() error {
	mdh.mu.Lock()
	defer mdh.mu.Unlock()
	
	if !mdh.running {
		return fmt.Errorf("handler is not running")
	}
	
	// Get listen key for user data stream
	listenKey, err := mdh.client.NewStartUserStreamService().Do(context.Background())
	if err != nil {
		return fmt.Errorf("failed to get listen key: %w", err)
	}
	
	// Subscribe to user data updates
	doneC, stopC, err := futures.WsUserDataServe(listenKey, mdh.handleUserDataUpdate, mdh.handleError)
	if err != nil {
		return fmt.Errorf("failed to subscribe to user data updates: %w", err)
	}
	
	conn := &WebSocketConnection{
		endpoint:   "userData",
		doneC:      doneC,
		stopC:      stopC,
		connected:  true,
		lastPing:   time.Now(),
	}
	
	mdh.connections["userData"] = conn
	mdh.statistics.TotalConnections++
	mdh.statistics.ActiveConnections++
	
	// Start listen key keepalive
	go mdh.keepaliveListenKey(listenKey)
	
	return nil
}

// GetCurrentPrice returns the current price data
func (mdh *MarketDataHandlerImpl) GetCurrentPrice() *PriceData {
	mdh.mu.RLock()
	defer mdh.mu.RUnlock()
	
	if mdh.currentPrice == nil {
		return nil
	}
	
	// Return a copy to prevent external modification
	price := *mdh.currentPrice
	return &price
}

// GetCurrentDepth returns the current depth data
func (mdh *MarketDataHandlerImpl) GetCurrentDepth() *DepthData {
	mdh.mu.RLock()
	defer mdh.mu.RUnlock()
	
	if mdh.currentDepth == nil {
		return nil
	}
	
	// Return a copy to prevent external modification
	depth := *mdh.currentDepth
	return &depth
}

// GetMarketCondition returns the current market condition
func (mdh *MarketDataHandlerImpl) GetMarketCondition() *MarketCondition {
	mdh.mu.RLock()
	defer mdh.mu.RUnlock()
	
	if mdh.currentPrice == nil {
		return nil
	}
	
	condition := &MarketCondition{
		Symbol:         mdh.symbol,
		Price:          mdh.currentPrice.Price,
		MarkPrice:      mdh.currentPrice.MarkPrice,
		Timestamp:      mdh.currentPrice.Timestamp,
		Volume24h:      mdh.currentPrice.Volume24h,
		PriceChange24h: mdh.currentPrice.Change24h,
		Volatility:     mdh.calculateVolatility(),
		Trend:          mdh.calculateTrend(),
	}
	
	// Add order book data if available
	if mdh.currentDepth != nil && len(mdh.currentDepth.Bids) > 0 && len(mdh.currentDepth.Asks) > 0 {
		condition.BidPrice = mdh.currentDepth.Bids[0].Price
		condition.AskPrice = mdh.currentDepth.Asks[0].Price
		condition.Spread = condition.AskPrice - condition.BidPrice
		
		if condition.BidPrice > 0 {
			condition.SpreadPercent = (condition.Spread / condition.BidPrice) * 100
		}
		
		// Calculate depth
		for _, bid := range mdh.currentDepth.Bids {
			condition.BidDepth += bid.Quantity * bid.Price
		}
		for _, ask := range mdh.currentDepth.Asks {
			condition.AskDepth += ask.Quantity * ask.Price
		}
		condition.TotalDepth = condition.BidDepth + condition.AskDepth
	}
	
	return condition
}

// GetPriceUpdates returns the price updates channel
func (mdh *MarketDataHandlerImpl) GetPriceUpdates() <-chan *PriceUpdate {
	return mdh.priceUpdates
}

// GetDepthUpdates returns the depth updates channel
func (mdh *MarketDataHandlerImpl) GetDepthUpdates() <-chan *DepthUpdate {
	return mdh.depthUpdates
}

// GetUserDataUpdates returns the user data updates channel
func (mdh *MarketDataHandlerImpl) GetUserDataUpdates() <-chan *UserDataUpdate {
	return mdh.userUpdates
}

// GetMarketDataEvents returns the market data events channel
func (mdh *MarketDataHandlerImpl) GetMarketDataEvents() <-chan *MarketDataEvent {
	return mdh.eventChan
}

// GetConnectionStatus returns the status of all connections
func (mdh *MarketDataHandlerImpl) GetConnectionStatus() map[string]ConnectionStatus {
	mdh.mu.RLock()
	defer mdh.mu.RUnlock()
	
	status := make(map[string]ConnectionStatus)
	for name, conn := range mdh.connections {
		if conn.connected {
			status[name] = ConnectionStatusConnected
		} else {
			status[name] = ConnectionStatusDisconnected
		}
	}
	
	return status
}

// Reconnect attempts to reconnect all disconnected connections
func (mdh *MarketDataHandlerImpl) Reconnect() error {
	mdh.mu.Lock()
	defer mdh.mu.Unlock()
	
	var lastError error
	reconnected := 0
	
	for name, conn := range mdh.connections {
		if !conn.connected {
			if err := mdh.reconnectConnection(name, conn); err != nil {
				lastError = err
			} else {
				reconnected++
			}
		}
	}
	
	if reconnected > 0 {
		mdh.sendEvent(MarketDataEventConnectionRestored, nil, 
			fmt.Sprintf("Reconnected %d connections", reconnected))
	}
	
	return lastError
}

// UpdateConfig updates the configuration
func (mdh *MarketDataHandlerImpl) UpdateConfig(cfg *config.Config) error {
	mdh.mu.Lock()
	defer mdh.mu.Unlock()
	
	mdh.config = cfg
	return nil
}

// SetSymbol sets the trading symbol
func (mdh *MarketDataHandlerImpl) SetSymbol(symbol string) error {
	mdh.mu.Lock()
	defer mdh.mu.Unlock()
	
	if mdh.running {
		return fmt.Errorf("cannot change symbol while handler is running")
	}
	
	mdh.symbol = symbol
	return nil
}

// Helper methods

// sendEvent sends an event to the event channel
func (mdh *MarketDataHandlerImpl) sendEvent(eventType MarketDataEventType, data interface{}, message string) {
	event := &MarketDataEvent{
		Type:      eventType,
		Timestamp: time.Now(),
		Symbol:    mdh.symbol,
		Data:      data,
		Message:   message,
	}

	select {
	case mdh.eventChan <- event:
		// Event sent successfully
	default:
		// Channel is full, skip event (non-blocking)
	}
}

// stopConnection stops a WebSocket connection
func (mdh *MarketDataHandlerImpl) stopConnection(name string, conn *WebSocketConnection) {
	if conn.stopC != nil {
		select {
		case <-conn.stopC:
			// Already closed
		default:
			close(conn.stopC)
		}
	}
	conn.connected = false
	mdh.statistics.ActiveConnections--
}

// reconnectConnection attempts to reconnect a WebSocket connection
func (mdh *MarketDataHandlerImpl) reconnectConnection(name string, conn *WebSocketConnection) error {
	conn.reconnectCount++
	mdh.statistics.TotalReconnects++
	now := time.Now()
	mdh.statistics.LastReconnectTime = &now

	// Implement reconnection logic based on connection type
	switch name {
	case "markPrice":
		return mdh.SubscribePriceUpdates()
	case "depth":
		return mdh.SubscribeDepthUpdates()
	case "userData":
		return mdh.SubscribeUserDataUpdates()
	default:
		return fmt.Errorf("unknown connection type: %s", name)
	}
}

// calculateVolatility calculates price volatility from price history
func (mdh *MarketDataHandlerImpl) calculateVolatility() float64 {
	if len(mdh.priceHistory) < 2 {
		return 0
	}

	// Calculate standard deviation of price changes
	var sum, sumSquares float64
	n := len(mdh.priceHistory) - 1

	for i := 1; i < len(mdh.priceHistory); i++ {
		change := (mdh.priceHistory[i] - mdh.priceHistory[i-1]) / mdh.priceHistory[i-1]
		sum += change
		sumSquares += change * change
	}

	mean := sum / float64(n)
	variance := (sumSquares / float64(n)) - (mean * mean)

	return math.Sqrt(variance) * 100 // Return as percentage
}

// calculateTrend calculates the current price trend
func (mdh *MarketDataHandlerImpl) calculateTrend() TrendDirection {
	if len(mdh.priceHistory) < 10 {
		return TrendUnknown
	}

	// Simple trend calculation based on recent price movements
	recent := mdh.priceHistory[len(mdh.priceHistory)-10:]
	start := recent[0]
	end := recent[len(recent)-1]

	change := (end - start) / start * 100

	if change > 1.0 {
		return TrendUp
	} else if change < -1.0 {
		return TrendDown
	}

	return TrendSideways
}

// addPriceToHistory adds a price to the price history for volatility calculation
func (mdh *MarketDataHandlerImpl) addPriceToHistory(price float64) {
	mdh.priceHistory = append(mdh.priceHistory, price)

	// Keep only the last maxHistorySize prices
	if len(mdh.priceHistory) > mdh.maxHistorySize {
		mdh.priceHistory = mdh.priceHistory[1:]
	}
}

// Background workers

// statisticsWorker updates statistics periodically
func (mdh *MarketDataHandlerImpl) statisticsWorker() {
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if mdh.IsRunning() {
				mdh.updateStatistics()
			}
		case <-mdh.stopChan:
			return
		}
	}
}

// connectionMonitor monitors WebSocket connections
func (mdh *MarketDataHandlerImpl) connectionMonitor() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if mdh.IsRunning() {
				mdh.checkConnections()
			}
		case <-mdh.stopChan:
			return
		}
	}
}

// updateStatistics updates internal statistics
func (mdh *MarketDataHandlerImpl) updateStatistics() {
	mdh.mu.Lock()
	defer mdh.mu.Unlock()

	mdh.statistics.Uptime = time.Since(mdh.statistics.StartTime)

	// Calculate uptime percentage (assuming 24/7 operation)
	totalTime := time.Since(mdh.statistics.StartTime)
	if totalTime > 0 {
		mdh.statistics.UptimePercentage = (mdh.statistics.Uptime.Seconds() / totalTime.Seconds()) * 100
	}
}

// checkConnections checks the health of WebSocket connections
func (mdh *MarketDataHandlerImpl) checkConnections() {
	mdh.mu.Lock()
	defer mdh.mu.Unlock()

	for name, conn := range mdh.connections {
		// Check if connection is stale (no ping for too long)
		if time.Since(conn.lastPing) > mdh.config.Advanced.WebSocket.PingInterval*2 {
			mdh.sendEvent(MarketDataEventConnectionLost, nil,
				fmt.Sprintf("Connection %s appears to be stale", name))

			// Attempt reconnection
			go func(n string, c *WebSocketConnection) {
				if err := mdh.reconnectConnection(n, c); err != nil {
					mdh.sendEvent(MarketDataEventError, nil,
						fmt.Sprintf("Failed to reconnect %s: %v", n, err))
				}
			}(name, conn)
		}
	}
}

// keepaliveListenKey keeps the user data listen key alive
func (mdh *MarketDataHandlerImpl) keepaliveListenKey(listenKey string) {
	ticker := time.NewTicker(30 * time.Minute) // Keepalive every 30 minutes
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if mdh.IsRunning() {
				err := mdh.client.NewKeepaliveUserStreamService().
					ListenKey(listenKey).
					Do(context.Background())
				if err != nil {
					mdh.sendEvent(MarketDataEventError, nil,
						fmt.Sprintf("Failed to keepalive listen key: %v", err))
				}
			}
		case <-mdh.stopChan:
			return
		}
	}
}
