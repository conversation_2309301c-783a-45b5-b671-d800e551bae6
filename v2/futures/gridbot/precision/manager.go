package precision

import (
	"context"
	"fmt"
	"strconv"
	"sync"
	"time"

	"github.com/adshao/go-binance/v2/futures"
	"github.com/adshao/go-binance/v2/futures/gridbot/config"
)

// PrecisionManager manages precision information for trading symbols
type PrecisionManager struct {
	client       *futures.Client
	config       *config.Config
	symbolInfo   map[string]*SymbolPrecision
	mu           sync.RWMutex
	lastUpdate   time.Time
	updateTTL    time.Duration
}

// SymbolPrecision holds precision information for a trading symbol
type SymbolPrecision struct {
	Symbol            string  `json:"symbol"`
	PricePrecision    int     `json:"price_precision"`
	QuantityPrecision int     `json:"quantity_precision"`
	BaseAssetPrecision int    `json:"base_asset_precision"`
	QuotePrecision    int     `json:"quote_precision"`
	MinPrice          float64 `json:"min_price"`
	MaxPrice          float64 `json:"max_price"`
	TickSize          float64 `json:"tick_size"`
	MinQuantity       float64 `json:"min_quantity"`
	MaxQuantity       float64 `json:"max_quantity"`
	StepSize          float64 `json:"step_size"`
	MinNotional       float64 `json:"min_notional"`
	LastUpdated       time.Time `json:"last_updated"`
}

// NewPrecisionManager creates a new precision manager
func NewPrecisionManager(client *futures.Client, cfg *config.Config) *PrecisionManager {
	return &PrecisionManager{
		client:     client,
		config:     cfg,
		symbolInfo: make(map[string]*SymbolPrecision),
		updateTTL:  time.Hour, // Cache for 1 hour
	}
}

// GetSymbolPrecision returns precision information for a symbol
func (pm *PrecisionManager) GetSymbolPrecision(symbol string) (*SymbolPrecision, error) {
	pm.mu.RLock()
	if info, exists := pm.symbolInfo[symbol]; exists && time.Since(info.LastUpdated) < pm.updateTTL {
		pm.mu.RUnlock()
		return info, nil
	}
	pm.mu.RUnlock()

	// Need to fetch fresh data
	return pm.fetchSymbolPrecision(symbol)
}

// fetchSymbolPrecision fetches precision information from Binance API
func (pm *PrecisionManager) fetchSymbolPrecision(symbol string) (*SymbolPrecision, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	exchangeInfo, err := pm.client.NewExchangeInfoService().Do(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch exchange info: %w", err)
	}

	// Find the symbol in the exchange info
	var targetSymbol *futures.Symbol
	for _, s := range exchangeInfo.Symbols {
		if s.Symbol == symbol {
			targetSymbol = &s
			break
		}
	}

	if targetSymbol == nil {
		return nil, fmt.Errorf("symbol %s not found in exchange info", symbol)
	}

	// Extract precision information
	precision := &SymbolPrecision{
		Symbol:             targetSymbol.Symbol,
		PricePrecision:     targetSymbol.PricePrecision,
		QuantityPrecision:  targetSymbol.QuantityPrecision,
		BaseAssetPrecision: targetSymbol.BaseAssetPrecision,
		QuotePrecision:     targetSymbol.QuotePrecision,
		LastUpdated:        time.Now(),
	}

	// Extract filter information
	if priceFilter := targetSymbol.PriceFilter(); priceFilter != nil {
		precision.MinPrice, _ = strconv.ParseFloat(priceFilter.MinPrice, 64)
		precision.MaxPrice, _ = strconv.ParseFloat(priceFilter.MaxPrice, 64)
		precision.TickSize, _ = strconv.ParseFloat(priceFilter.TickSize, 64)
	}

	if lotSizeFilter := targetSymbol.LotSizeFilter(); lotSizeFilter != nil {
		precision.MinQuantity, _ = strconv.ParseFloat(lotSizeFilter.MinQuantity, 64)
		precision.MaxQuantity, _ = strconv.ParseFloat(lotSizeFilter.MaxQuantity, 64)
		precision.StepSize, _ = strconv.ParseFloat(lotSizeFilter.StepSize, 64)
	}

	if minNotionalFilter := targetSymbol.MinNotionalFilter(); minNotionalFilter != nil {
		precision.MinNotional, _ = strconv.ParseFloat(minNotionalFilter.Notional, 64)
	}

	// Cache the result
	pm.mu.Lock()
	pm.symbolInfo[symbol] = precision
	pm.lastUpdate = time.Now()
	pm.mu.Unlock()

	return precision, nil
}

// FormatPrice formats a price value according to the symbol's precision
func (pm *PrecisionManager) FormatPrice(symbol string, price float64) (string, error) {
	precision, err := pm.GetSymbolPrecision(symbol)
	if err != nil {
		return "", err
	}

	return strconv.FormatFloat(price, 'f', precision.PricePrecision, 64), nil
}

// FormatQuantity formats a quantity value according to the symbol's precision
func (pm *PrecisionManager) FormatQuantity(symbol string, quantity float64) (string, error) {
	precision, err := pm.GetSymbolPrecision(symbol)
	if err != nil {
		return "", err
	}

	return strconv.FormatFloat(quantity, 'f', precision.QuantityPrecision, 64), nil
}

// ValidatePrice validates if a price is within the symbol's constraints
func (pm *PrecisionManager) ValidatePrice(symbol string, price float64) error {
	precision, err := pm.GetSymbolPrecision(symbol)
	if err != nil {
		return err
	}

	if price < precision.MinPrice {
		return fmt.Errorf("price %.8f is below minimum %.8f for %s", price, precision.MinPrice, symbol)
	}

	if price > precision.MaxPrice {
		return fmt.Errorf("price %.8f is above maximum %.8f for %s", price, precision.MaxPrice, symbol)
	}

	// Check tick size alignment using the same logic as rounding
	if precision.TickSize > 0 {
		ticks := price / precision.TickSize
		roundedTicks := float64(int64(ticks + 0.5))
		expectedPrice := roundedTicks * precision.TickSize

		// Check if the price matches what we would get after rounding
		diff := price - expectedPrice
		if diff < 0 {
			diff = -diff
		}
		if diff > 1e-8 { // Allow for floating point precision errors
			return fmt.Errorf("price %.8f does not align with tick size %.8f for %s", price, precision.TickSize, symbol)
		}
	}

	return nil
}

// ValidateQuantity validates if a quantity is within the symbol's constraints
func (pm *PrecisionManager) ValidateQuantity(symbol string, quantity float64) error {
	precision, err := pm.GetSymbolPrecision(symbol)
	if err != nil {
		return err
	}

	if quantity < precision.MinQuantity {
		return fmt.Errorf("quantity %.8f is below minimum %.8f for %s", quantity, precision.MinQuantity, symbol)
	}

	if quantity > precision.MaxQuantity {
		return fmt.Errorf("quantity %.8f is above maximum %.8f for %s", quantity, precision.MaxQuantity, symbol)
	}

	// Check step size alignment using the same logic as rounding
	if precision.StepSize > 0 {
		// For step size 1.0, use simple validation
		if precision.StepSize == 1.0 {
			expectedQuantity := float64(int(quantity + 0.5))
			diff := quantity - expectedQuantity
			if diff < 0 {
				diff = -diff
			}
			if diff > 1e-8 {
				return fmt.Errorf("quantity %.8f does not align with step size %.8f for %s", quantity, precision.StepSize, symbol)
			}
		} else {
			// For other step sizes, use the same logic as rounding
			steps := quantity / precision.StepSize
			roundedSteps := float64(int64(steps + 0.5))
			expectedQuantity := roundedSteps * precision.StepSize

			diff := quantity - expectedQuantity
			if diff < 0 {
				diff = -diff
			}
			if diff > 1e-8 {
				return fmt.Errorf("quantity %.8f does not align with step size %.8f for %s", quantity, precision.StepSize, symbol)
			}
		}
	}

	return nil
}

// ValidateNotional validates if the order notional value meets minimum requirements
func (pm *PrecisionManager) ValidateNotional(symbol string, price, quantity float64) error {
	precision, err := pm.GetSymbolPrecision(symbol)
	if err != nil {
		return err
	}

	notional := price * quantity
	if notional < precision.MinNotional {
		return fmt.Errorf("notional value %.8f is below minimum %.8f for %s", notional, precision.MinNotional, symbol)
	}

	return nil
}

// GetDisplayPrecision returns the appropriate precision for display purposes
func (pm *PrecisionManager) GetDisplayPrecision(symbol string) (int, error) {
	precision, err := pm.GetSymbolPrecision(symbol)
	if err != nil {
		return 2, err // Default to 2 decimal places
	}

	return precision.PricePrecision, nil
}

// RefreshCache forces a refresh of the precision cache
func (pm *PrecisionManager) RefreshCache() error {
	pm.mu.Lock()
	defer pm.mu.Unlock()

	// Clear the cache
	pm.symbolInfo = make(map[string]*SymbolPrecision)
	pm.lastUpdate = time.Time{}

	return nil
}

// GetCachedSymbols returns a list of symbols currently cached
func (pm *PrecisionManager) GetCachedSymbols() []string {
	pm.mu.RLock()
	defer pm.mu.RUnlock()

	symbols := make([]string, 0, len(pm.symbolInfo))
	for symbol := range pm.symbolInfo {
		symbols = append(symbols, symbol)
	}

	return symbols
}

// RoundPrice rounds a price to align with the symbol's tick size
func (pm *PrecisionManager) RoundPrice(symbol string, price float64) (float64, error) {
	precision, err := pm.GetSymbolPrecision(symbol)
	if err != nil {
		return price, err
	}

	if precision.TickSize <= 0 {
		return price, nil
	}

	// Simple and reliable rounding: divide by tick size, round, then multiply back
	// This avoids floating point precision issues
	ticks := price / precision.TickSize
	roundedTicks := float64(int64(ticks + 0.5))
	rounded := roundedTicks * precision.TickSize

	// Ensure it's within bounds
	if rounded < precision.MinPrice {
		rounded = precision.MinPrice
	}
	if rounded > precision.MaxPrice {
		rounded = precision.MaxPrice
	}

	return rounded, nil
}

// RoundQuantity rounds a quantity to align with the symbol's step size
func (pm *PrecisionManager) RoundQuantity(symbol string, quantity float64) (float64, error) {
	precision, err := pm.GetSymbolPrecision(symbol)
	if err != nil {
		return quantity, err
	}

	if precision.StepSize <= 0 {
		return quantity, nil
	}

	// Use more precise rounding to avoid floating point errors
	// For step size 1.0, we can use simple rounding
	if precision.StepSize == 1.0 {
		rounded := float64(int(quantity + 0.5))

		// Ensure it's within bounds
		if rounded < precision.MinQuantity {
			rounded = precision.MinQuantity
		}
		if rounded > precision.MaxQuantity {
			rounded = precision.MaxQuantity
		}

		return rounded, nil
	}

	// For other step sizes, use integer arithmetic
	stepSizeInt := int64(precision.StepSize * 1e8) // Convert to integer (8 decimal places)
	quantityInt := int64(quantity * 1e8)

	// Round to nearest step
	remainder := quantityInt % stepSizeInt
	if remainder >= stepSizeInt/2 {
		quantityInt += (stepSizeInt - remainder)
	} else {
		quantityInt -= remainder
	}

	rounded := float64(quantityInt) / 1e8

	// Ensure it's within bounds
	if rounded < precision.MinQuantity {
		rounded = precision.MinQuantity
	}
	if rounded > precision.MaxQuantity {
		rounded = precision.MaxQuantity
	}

	return rounded, nil
}

// AdjustOrderForPrecision adjusts both price and quantity to meet exchange requirements
func (pm *PrecisionManager) AdjustOrderForPrecision(symbol string, price, quantity float64) (adjustedPrice, adjustedQuantity float64, err error) {
	// Round price to tick size
	adjustedPrice, err = pm.RoundPrice(symbol, price)
	if err != nil {
		return price, quantity, fmt.Errorf("failed to round price: %w", err)
	}

	// Round quantity to step size
	adjustedQuantity, err = pm.RoundQuantity(symbol, quantity)
	if err != nil {
		return adjustedPrice, quantity, fmt.Errorf("failed to round quantity: %w", err)
	}

	// Validate the adjusted values
	if err := pm.ValidatePrice(symbol, adjustedPrice); err != nil {
		return adjustedPrice, adjustedQuantity, fmt.Errorf("adjusted price validation failed: %w", err)
	}

	if err := pm.ValidateQuantity(symbol, adjustedQuantity); err != nil {
		return adjustedPrice, adjustedQuantity, fmt.Errorf("adjusted quantity validation failed: %w", err)
	}

	if err := pm.ValidateNotional(symbol, adjustedPrice, adjustedQuantity); err != nil {
		return adjustedPrice, adjustedQuantity, fmt.Errorf("adjusted notional validation failed: %w", err)
	}

	return adjustedPrice, adjustedQuantity, nil
}

// PrintSymbolInfo prints detailed information about a symbol (for debugging)
func (pm *PrecisionManager) PrintSymbolInfo(symbol string) error {
	precision, err := pm.GetSymbolPrecision(symbol)
	if err != nil {
		return err
	}

	fmt.Printf("\n=== Symbol Precision Info: %s ===\n", symbol)
	fmt.Printf("Price Precision: %d decimal places\n", precision.PricePrecision)
	fmt.Printf("Quantity Precision: %d decimal places\n", precision.QuantityPrecision)
	fmt.Printf("Base Asset Precision: %d\n", precision.BaseAssetPrecision)
	fmt.Printf("Quote Precision: %d\n", precision.QuotePrecision)
	fmt.Printf("Price Range: %.8f - %.8f\n", precision.MinPrice, precision.MaxPrice)
	fmt.Printf("Tick Size: %.8f\n", precision.TickSize)
	fmt.Printf("Quantity Range: %.8f - %.8f\n", precision.MinQuantity, precision.MaxQuantity)
	fmt.Printf("Step Size: %.8f\n", precision.StepSize)
	fmt.Printf("Min Notional: %.8f\n", precision.MinNotional)
	fmt.Printf("Last Updated: %s\n", precision.LastUpdated.Format("2006-01-02 15:04:05"))
	fmt.Printf("=====================================\n")

	return nil
}
