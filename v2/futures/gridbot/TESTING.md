# Grid Trading Strategy Integration Tests

This document describes the comprehensive integration test suite for all three grid trading strategy types: Neutral, Long, and Short.

## 🧪 Test Overview

The test suite validates the complete functionality of each grid trading strategy using Binance testnet with real market data and WebSocket connections.

### Test Coverage

- ✅ **Grid Level Calculation**: Validates proper price distribution for each strategy type
- ✅ **Order Distribution Logic**: Ensures buy/sell ratios match strategy expectations
- ✅ **WebSocket Integration**: Validates real-time market data processing
- ✅ **Binance Testnet**: Tests actual API connectivity and authentication
- ✅ **Component Integration**: Ensures all modules work together seamlessly

## 📁 Test Configuration Files

### 1. Neutral Grid Strategy (`config/examples/neutral_grid_test.yaml`)
```yaml
# Characteristics:
- Equal buy/sell order distribution around mid-price
- Balanced quantities for market-neutral positioning
- Suitable for ranging/sideways markets
- Lower risk profile

# Test Parameters:
- Symbol: BTCUSDT
- Leverage: 2x
- Initial Margin: 25,000 USDT
- Grid Range: 117,000 - 119,000 USDT
- Grid Count: 6 levels
```

### 2. Long Grid Strategy (`config/examples/long_grid_test.yaml`)
```yaml
# Characteristics:
- Buy-heavy distribution (more buy orders in lower 70% of range)
- Optimized for upward trending markets
- Accumulates on dips, sells on rallies
- Medium to high risk profile

# Test Parameters:
- Symbol: BTCUSDT
- Leverage: 2x
- Initial Margin: 29,000 USDT
- Grid Range: 117,000 - 119,000 USDT
- Grid Count: 4 levels
- Calculation: Geometric spacing
```

### 3. Short Grid Strategy (`config/examples/short_grid_test.yaml`)
```yaml
# Characteristics:
- Sell-heavy distribution (more sell orders in upper 70% of range)
- Optimized for downward trending markets
- Sells on rallies, covers on dips
- Medium to high risk profile

# Test Parameters:
- Symbol: BTCUSDT
- Leverage: 2x
- Initial Margin: 29,000 USDT
- Grid Range: 117,500 - 119,000 USDT
- Grid Count: 4 levels
- Calculation: Arithmetic spacing
```

## 🚀 Running the Tests

### Individual Strategy Tests

```bash
# Test Neutral Grid Strategy
go run cmd/test_neutral_grid/main.go

# Test Long Grid Strategy  
go run cmd/test_long_grid/main.go

# Test Short Grid Strategy
go run cmd/test_short_grid/main.go
```

### Comprehensive Test Suite

```bash
# Run all strategy tests sequentially
go run cmd/test_all_strategies/main.go
```

## 📊 Test Results Validation

### Expected Outcomes by Strategy Type

#### Neutral Strategy ✅
- **Order Distribution**: ~50% buy orders, ~50% sell orders
- **Quantity Distribution**: Equal buy and sell quantities (ratio ~1.0)
- **Price Distribution**: Orders evenly distributed around mid-price
- **Market Bias**: None (neutral positioning)

#### Long Strategy ✅
- **Order Distribution**: More buy orders in lower price regions
- **Quantity Distribution**: Buy quantity > Sell quantity (buy-heavy)
- **Price Distribution**: 70% of buy orders below mid-price threshold
- **Market Bias**: Bullish (optimized for uptrends)

#### Short Strategy ✅
- **Order Distribution**: More sell orders in upper price regions
- **Quantity Distribution**: Sell quantity > Buy quantity (sell-heavy)
- **Price Distribution**: 70% of sell orders above mid-price threshold
- **Market Bias**: Bearish (optimized for downtrends)

## 🔧 Test Environment Setup

### Prerequisites
- Go 1.19+ installed
- Network access to Binance testnet
- Valid testnet API credentials (provided in configurations)

### Testnet Account Details
- **API Key**: aab8ef750fcc27743c62c613f4089a8c0a47f083cd15cf4b0c0843faa6efb798
- **Secret Key**: 11f3f1ffcd5ef5069bef8ab5ee786572420213ca4d657cfec5b1603fb21406ca
- **Available Balance**: 30,000 USDT
- **Environment**: Binance Futures Testnet

### Safety Features
- All tests run in **dry-run mode** by default
- No real orders are placed during testing
- Comprehensive error handling and graceful shutdown
- Automatic cleanup of resources after each test

## 📈 Performance Metrics

### Test Execution Times
- Individual strategy test: ~20-30 seconds
- Comprehensive test suite: ~60-90 seconds
- WebSocket data collection: 15 seconds per test

### Resource Requirements
- Memory: ~50MB per test
- CPU: Minimal (mostly I/O bound)
- Network: Continuous WebSocket connections during tests



## 🔍 Troubleshooting

### Common Issues and Solutions

#### Insufficient Margin Error
```
Error: insufficient initial margin: required X, available Y
```
**Solution**: Increase `initial_margin` in configuration file or reduce `grid_count`

#### WebSocket Connection Issues
```
Error: failed to subscribe to price updates
```
**Solution**: Check network connectivity and API credentials

#### API Rate Limiting
```
Error: rate limit exceeded
```
**Solution**: Add delays between tests or reduce update frequency

## 📋 Test Checklist

Before running tests, ensure:

- [ ] Go environment is properly set up
- [ ] Network connectivity to Binance testnet
- [ ] Configuration files are present and valid
- [ ] Sufficient testnet balance available
- [ ] No other processes using the same API credentials

## 🎯 Success Criteria

A test is considered successful when:

1. ✅ Grid calculation produces expected distribution
2. ✅ All components initialize without errors
3. ✅ WebSocket connections establish successfully
4. ✅ Real-time data processing works correctly
5. ✅ Graceful shutdown completes without errors

## 📞 Support

For issues with the test suite:

1. Check the troubleshooting section above
2. Verify configuration parameters
3. Ensure testnet connectivity
4. Review log files for detailed error messages

## 🔄 Continuous Integration

These tests can be integrated into CI/CD pipelines:

```bash
# CI/CD command
go run cmd/test_all_strategies/main.go
```

Exit codes:
- `0`: All tests passed
- `1`: One or more tests failed
