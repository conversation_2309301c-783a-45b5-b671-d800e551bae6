# Futures Grid Trading Bot

A sophisticated automated grid trading bot for Binance Futures that implements grid trading strategies with real-time monitoring.

## Architecture Overview

The grid trading bot is designed with a modular architecture consisting of several key components:

### Core Components

1. **Configuration System** (`config/`)
   - YAML-based configuration management
   - Strategy parameters and risk settings
   - Environment-specific configurations

2. **Grid Strategy Engine** (`strategy/`)
   - Grid calculation algorithms (arithmetic/geometric)
   - Order placement logic
   - Grid maintenance and rebalancing

3. **Order Management System** (`orders/`)
   - Order lifecycle management
   - Order state tracking and synchronization
   - Error handling and retry mechanisms

4. **Market Data Handler** (`market/`)
   - Real-time price feed via WebSocket
   - Order book monitoring
   - Market condition analysis

5. **Bot Controller** (`bot/`)
   - Main orchestration logic
   - Component coordination
   - Lifecycle management

7. **Logging & Monitoring** (`monitor/`)
   - Comprehensive logging system
   - Performance metrics
   - Trade history and analytics

## Grid Trading Strategy

### How It Works

1. **Grid Setup**: Define price range and divide into equal segments (grids)
2. **Order Placement**: Place buy/sell orders at each grid level
3. **Trade Execution**: When price hits a grid level, execute trade and place opposite order
4. **Profit Capture**: Capture profits from price oscillations within the range
5. **Grid Maintenance**: Continuously maintain grid structure as trades execute

### Strategy Types

- **Neutral Grid**: Profits from price oscillations in both directions
- **Long Grid**: Optimized for upward trending markets
- **Short Grid**: Optimized for downward trending markets

### Grid Calculation Methods

- **Arithmetic**: Equal price differences between grid levels
- **Geometric**: Equal percentage differences between grid levels

## Configuration

The bot uses YAML configuration files to define trading parameters:

```yaml
# Example configuration structure
trading:
  symbol: "BTCUSDT"
  leverage: 10
  initial_margin: 1000.0

grid:
  type: "neutral"  # neutral, long, short
  calculation: "arithmetic"  # arithmetic, geometric
  price_range:
    lower: 30000.0
    upper: 35000.0
  grid_count: 20



monitoring:
  log_level: "info"
  metrics_enabled: true
  trade_history: true
```

## Safety Features

- **Price Range Validation**: Bot stops if price moves outside defined range
- **Risk Limits**: Configurable stop-loss and take-profit triggers
- **Margin Monitoring**: Prevents liquidation by monitoring margin levels
- **Error Recovery**: Robust error handling and automatic recovery
- **Dry Run Mode**: Test strategies without real trading

## Getting Started

1. Configure your trading parameters in a YAML file
2. Set up your Binance Futures API credentials
3. Initialize and start the grid trading bot
4. Monitor performance through logs and metrics

## Directory Structure

```
gridbot/
├── README.md
├── config/
│   ├── config.go          # Configuration structures and loading
│   ├── validation.go      # Configuration validation
│   └── examples/          # Example configuration files
├── strategy/
│   ├── grid.go           # Grid calculation and management
│   ├── types.go          # Strategy-related types
│   └── calculator.go     # Grid price calculations
├── orders/
│   ├── manager.go        # Order management system
│   ├── tracker.go        # Order state tracking
│   └── types.go          # Order-related types
├── market/
│   ├── data.go           # Market data handler
│   ├── websocket.go      # WebSocket connections
│   └── types.go          # Market data types
├── bot/
│   ├── controller.go     # Main bot controller
│   ├── lifecycle.go      # Bot lifecycle management
│   └── types.go          # Bot-related types
├── monitor/
│   ├── logger.go         # Logging system
│   ├── metrics.go        # Performance metrics
│   └── history.go        # Trade history tracking
├── cmd/                     # Executable commands
│   └── basic_grid/         # Basic grid trading example
│       └── main.go         # Main executable
└── examples/               # Additional examples and configs
    └── configs/            # Example YAML configurations
```

This architecture provides a solid foundation for implementing a robust and scalable futures grid trading bot with monitoring capabilities.

## Quick Start

### 1. Installation

```bash
# Clone the repository
git clone https://github.com/adshao/go-binance.git
cd go-binance/v2/futures/gridbot

# Install dependencies
go mod tidy
```

### 2. Configuration

Create a configuration file based on the examples:

```bash
# Copy example configuration
cp config/examples/basic_grid.yaml my_config.yaml

# Edit the configuration
nano my_config.yaml
```

Set your Binance API credentials:

```yaml
api:
  api_key: "your_binance_api_key_here"
  secret_key: "your_binance_secret_key_here"
  testnet: true  # Start with testnet for safety
```

### 3. Run the Bot

```bash
# Set environment variables (optional)
export BINANCE_API_KEY="your_api_key"
export BINANCE_SECRET_KEY="your_secret_key"

# Run the basic example
go run cmd/basic_grid/main.go
```

## Configuration Guide

### Basic Configuration

```yaml
# API Configuration
api:
  api_key: "your_api_key"
  secret_key: "your_secret_key"
  testnet: true  # Use testnet for testing

# Trading Parameters
trading:
  symbol: "BTCUSDT"      # Trading pair
  leverage: 5            # Leverage (1-125)
  initial_margin: 100.0  # Initial margin in USDT
  dry_run: true          # Test mode (no real trades)

# Grid Strategy
grid:
  type: "neutral"        # neutral, long, short
  calculation: "arithmetic"  # arithmetic, geometric
  price_range:
    lower: 30000.0       # Lower price bound
    upper: 35000.0       # Upper price bound
  grid_count: 10         # Number of grid levels
```

### Advanced Configuration

```yaml


# Advanced Settings
advanced:
  order_timeout: 30s
  max_retries: 3
  price_deviation: 0.1      # Price deviation tolerance %
  min_order_size: 0.001     # Minimum order size
  rebalance_interval: 5m    # Grid rebalancing interval
```

## Usage Examples

### Basic Grid Trading

```go
package main

import (
    "github.com/adshao/go-binance/v2/futures"
    "github.com/adshao/go-binance/v2/futures/gridbot/config"
    "github.com/adshao/go-binance/v2/futures/gridbot/strategy"
)

func main() {
    // Load configuration
    cfg, err := config.LoadConfig("config.yaml")
    if err != nil {
        panic(err)
    }

    // Create strategy
    strategy := strategy.NewGridStrategy(cfg)

    // Initialize and start
    strategy.Initialize()
    strategy.Start()

    // Monitor performance
    stats := strategy.GetStatistics()
    fmt.Printf("ROI: %.2f%%\n", stats.ROI)
}
```

### Custom Grid Levels

```yaml
grid:
  type: "neutral"
  custom_levels: [30000, 30500, 31000, 31500, 32000, 32500, 33000]
```

### Multiple Strategies

```go
// Run multiple strategies simultaneously
strategies := []*strategy.GridStrategy{
    strategy.NewGridStrategy(btcConfig),
    strategy.NewGridStrategy(ethConfig),
    strategy.NewGridStrategy(adaConfig),
}

for _, s := range strategies {
    go s.Start()
}
```



## Monitoring and Logging

### Log Configuration

```yaml
monitoring:
  log_level: "info"              # debug, info, warn, error
  log_file: "gridbot.log"        # Log file path
  metrics_enabled: true          # Enable metrics collection
  trade_history: true            # Keep trade history
  webhook_url: "https://..."     # Slack/Discord webhook
```

### Performance Metrics

The bot tracks comprehensive performance metrics:

- **Trading Metrics**: Total trades, win rate, PnL, ROI
- **Risk Metrics**: Drawdown, Sharpe ratio, profit factor
- **Grid Metrics**: Grid efficiency, active levels, fill rate
- **System Metrics**: Uptime, error rate, latency

### Real-time Monitoring

```go
// Get real-time statistics
stats := strategy.GetStatistics()
fmt.Printf("Current PnL: %.2f USDT\n", stats.NetPnL)
fmt.Printf("Win Rate: %.2f%%\n", stats.WinRate)
fmt.Printf("Grid Efficiency: %.2f%%\n", stats.GridEfficiency)

// Monitor market conditions
condition := marketHandler.GetMarketCondition()
fmt.Printf("Price: %.2f, Volatility: %.2f%%\n",
    condition.Price, condition.Volatility)
```

## Best Practices

### 1. Start with Testnet

Always test your configuration on Binance testnet before using real funds:

```yaml
api:
  testnet: true
```

### 2. Use Dry Run Mode

Test your strategy without placing real orders:

```yaml
trading:
  dry_run: true
```

### 3. Monitor Performance

Regularly check bot performance and adjust parameters:

- Monitor win rate and PnL
- Check grid efficiency
- Analyze drawdown patterns
- Review error logs

### 5. Gradual Scaling

Start with small amounts and gradually increase:

```yaml
trading:
  initial_margin: 50.0   # Start small
  leverage: 2            # Use low leverage
```

## Troubleshooting

### Common Issues

1. **Connection Errors**
   - Check API credentials
   - Verify network connectivity
   - Ensure API permissions are set correctly

2. **Order Placement Failures**
   - Check account balance
   - Verify symbol is correct
   - Ensure order size meets minimum requirements

3. **High Error Rates**
   - Reduce order frequency
   - Increase retry delays
   - Check market conditions

### Debug Mode

Enable debug logging for detailed information:

```yaml
monitoring:
  log_level: "debug"
```

### Error Recovery

The bot includes automatic error recovery:

- Automatic reconnection for WebSocket failures
- Order retry mechanisms with exponential backoff
- Graceful handling of API rate limits

## Performance Optimization

### 1. WebSocket Configuration

Optimize WebSocket settings for your network:

```yaml
advanced:
  websocket:
    reconnect_interval: 5s
    ping_interval: 20s
    max_reconnects: 10
    buffer_size: 1000
```

### 2. Order Management

Tune order management parameters:

```yaml
advanced:
  order_timeout: 30s
  max_retries: 3
  retry_delay: 5s
```

### 3. Grid Optimization

Optimize grid parameters based on market conditions:

- Use more grids in volatile markets
- Adjust price range based on support/resistance
- Consider geometric grids for wider ranges

## Security Considerations

### API Key Security

- Use API keys with minimal required permissions
- Enable IP restrictions on Binance
- Store keys securely (environment variables)
- Regularly rotate API keys

### Risk Controls

- Set appropriate stop-loss levels
- Use position size limits
- Monitor margin levels closely
- Implement emergency stop mechanisms

## Support and Community

### Documentation

- [Binance Futures API Documentation](https://binance-docs.github.io/apidocs/futures/en/)
- [Go-Binance Library Documentation](https://godoc.org/github.com/adshao/go-binance)

### Community

- GitHub Issues: Report bugs and feature requests
- Discord/Telegram: Community discussions
- Stack Overflow: Technical questions

### Contributing

Contributions are welcome! Please:

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

## License

This project is licensed under the MIT License. See LICENSE file for details.

## Disclaimer

**⚠️ IMPORTANT DISCLAIMER ⚠️**

This software is for educational and research purposes only. Trading cryptocurrencies involves substantial risk of loss and is not suitable for all investors. The authors and contributors are not responsible for any financial losses incurred through the use of this software.

**Key Risks:**
- Cryptocurrency trading is highly volatile and risky
- Grid trading can result in significant losses during trending markets
- Leverage amplifies both profits and losses
- Technical failures can lead to unexpected losses
- Past performance does not guarantee future results

**Before using this software:**
- Thoroughly test on testnet with paper trading
- Start with small amounts you can afford to lose
- Understand the risks of grid trading strategies
- Monitor your positions actively

**Use at your own risk. Never invest more than you can afford to lose.**
