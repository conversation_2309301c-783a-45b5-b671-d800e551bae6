package config

import (
	"fmt"
	"strings"
)

// ValidateConfig validates the configuration for correctness and completeness
func ValidateConfig(config *Config) error {
	if err := validateAPI(&config.API); err != nil {
		return fmt.Errorf("API configuration error: %w", err)
	}

	if err := validateTrading(&config.Trading); err != nil {
		return fmt.Errorf("trading configuration error: %w", err)
	}

	if err := validateGrid(&config.Grid); err != nil {
		return fmt.Errorf("grid configuration error: %w", err)
	}



	if err := validateMonitoring(&config.Monitoring); err != nil {
		return fmt.Errorf("monitoring configuration error: %w", err)
	}

	if err := validateAdvanced(&config.Advanced); err != nil {
		return fmt.Errorf("advanced configuration error: %w", err)
	}

	return nil
}

// validateAPI validates API configuration
func validateAPI(api *APIConfig) error {
	if api.APIKey == "" {
		return fmt.Errorf("API key is required")
	}

	if api.SecretKey == "" {
		return fmt.Errorf("secret key is required")
	}

	return nil
}

// validateTrading validates trading configuration
func validateTrading(trading *TradingConfig) error {
	if trading.Symbol == "" {
		return fmt.Errorf("trading symbol is required")
	}

	// Validate symbol format (should be uppercase and contain USDT or USDC)
	if !strings.Contains(strings.ToUpper(trading.Symbol), "USDT") && !strings.Contains(strings.ToUpper(trading.Symbol), "USDC") {
		return fmt.Errorf("symbol should be a USDT or USDC pair (e.g., BTCUSDT)")
	}

	if trading.Leverage < 1 || trading.Leverage > 125 {
		return fmt.Errorf("leverage must be between 1 and 125, got %d", trading.Leverage)
	}

	if trading.InitialMargin <= 0 {
		return fmt.Errorf("initial margin must be positive, got %f", trading.InitialMargin)
	}

	return nil
}

// validateGrid validates grid configuration
func validateGrid(grid *GridConfig) error {
	// Validate grid type
	switch grid.Type {
	case GridTypeNeutral, GridTypeLong, GridTypeShort:
		// Valid types
	default:
		return fmt.Errorf("invalid grid type: %s (must be neutral, long, or short)", grid.Type)
	}

	// Validate calculation type
	switch grid.Calculation {
	case CalculationArithmetic, CalculationGeometric:
		// Valid types
	default:
		return fmt.Errorf("invalid calculation type: %s (must be arithmetic or geometric)", grid.Calculation)
	}

	// Validate price range
	if grid.PriceRange.Lower <= 0 {
		return fmt.Errorf("lower price range must be positive, got %f", grid.PriceRange.Lower)
	}

	if grid.PriceRange.Upper <= 0 {
		return fmt.Errorf("upper price range must be positive, got %f", grid.PriceRange.Upper)
	}

	if grid.PriceRange.Lower >= grid.PriceRange.Upper {
		return fmt.Errorf("lower price range (%f) must be less than upper price range (%f)", 
			grid.PriceRange.Lower, grid.PriceRange.Upper)
	}

	// Validate grid count
	if grid.GridCount < 2 {
		return fmt.Errorf("grid count must be at least 2, got %d", grid.GridCount)
	}

	if grid.GridCount > 200 {
		return fmt.Errorf("grid count cannot exceed 200, got %d", grid.GridCount)
	}

	// Validate custom levels if provided
	if len(grid.CustomLevels) > 0 {
		if len(grid.CustomLevels) < 2 {
			return fmt.Errorf("custom levels must have at least 2 levels")
		}

		// Check if levels are sorted and within range
		for i, level := range grid.CustomLevels {
			if level <= 0 {
				return fmt.Errorf("custom level %d must be positive, got %f", i, level)
			}

			if level < grid.PriceRange.Lower || level > grid.PriceRange.Upper {
				return fmt.Errorf("custom level %d (%f) is outside price range [%f, %f]", 
					i, level, grid.PriceRange.Lower, grid.PriceRange.Upper)
			}

			if i > 0 && level <= grid.CustomLevels[i-1] {
				return fmt.Errorf("custom levels must be in ascending order")
			}
		}
	}

	return nil
}



// validateMonitoring validates monitoring configuration
func validateMonitoring(monitoring *MonitoringConfig) error {
	validLogLevels := []string{"debug", "info", "warn", "error", "fatal"}
	isValidLogLevel := false
	for _, level := range validLogLevels {
		if monitoring.LogLevel == level {
			isValidLogLevel = true
			break
		}
	}

	if !isValidLogLevel {
		return fmt.Errorf("invalid log level: %s (must be one of: %s)", 
			monitoring.LogLevel, strings.Join(validLogLevels, ", "))
	}

	if monitoring.UpdateInterval <= 0 {
		return fmt.Errorf("update interval must be positive")
	}

	return nil
}

// validateAdvanced validates advanced configuration
func validateAdvanced(advanced *AdvancedConfig) error {
	if advanced.OrderTimeout <= 0 {
		return fmt.Errorf("order timeout must be positive")
	}

	if advanced.MaxRetries < 0 {
		return fmt.Errorf("max retries must be non-negative, got %d", advanced.MaxRetries)
	}

	if advanced.RetryDelay <= 0 {
		return fmt.Errorf("retry delay must be positive")
	}

	if advanced.PriceDeviation < 0 || advanced.PriceDeviation > 10 {
		return fmt.Errorf("price deviation must be between 0 and 10 percent, got %f", advanced.PriceDeviation)
	}

	if advanced.MinOrderSize <= 0 {
		return fmt.Errorf("minimum order size must be positive, got %f", advanced.MinOrderSize)
	}

	if advanced.MaxOrderSize > 0 && advanced.MaxOrderSize < advanced.MinOrderSize {
		return fmt.Errorf("maximum order size (%f) must be greater than minimum order size (%f)", 
			advanced.MaxOrderSize, advanced.MinOrderSize)
	}

	if advanced.RebalanceInterval <= 0 {
		return fmt.Errorf("rebalance interval must be positive")
	}

	if advanced.HealthCheckInterval <= 0 {
		return fmt.Errorf("health check interval must be positive")
	}

	// Validate WebSocket config
	if advanced.WebSocket.ReconnectInterval <= 0 {
		return fmt.Errorf("WebSocket reconnect interval must be positive")
	}

	if advanced.WebSocket.PingInterval <= 0 {
		return fmt.Errorf("WebSocket ping interval must be positive")
	}

	if advanced.WebSocket.MaxReconnects < 0 {
		return fmt.Errorf("WebSocket max reconnects must be non-negative, got %d", advanced.WebSocket.MaxReconnects)
	}

	if advanced.WebSocket.BufferSize <= 0 {
		return fmt.Errorf("WebSocket buffer size must be positive, got %d", advanced.WebSocket.BufferSize)
	}

	return nil
}
