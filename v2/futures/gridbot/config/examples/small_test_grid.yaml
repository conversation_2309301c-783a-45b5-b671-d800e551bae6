# Small Test Grid Trading Configuration
# This configuration uses very small amounts for safe testing

# API Configuration
api:
  api_key: "uqmdgwUnig4OZabB3BWWY3IaKuyf6y1izlMU8Qz7RpBkJ0LP8FHoEdmYw3QnewmP"  # Will be overridden by BINANCE_API_KEY environment variable
  secret_key: "rom5DKGjk6IXfjL0a3m9S2wJDVuOnscz5ndyjK2AYRjoDpuoEtO89GCXS0JrRWxi"  # Will be overridden by BINANCE_SECRET_KEY environment variable
  testnet: false  # Set to false for live API but using small amounts for safety
  # proxy: "http://127.0.0.1:7890"  # Optional proxy

# Trading Configuration
trading:
  symbol: "JUPUSDT"
  leverage: 5  # Lower leverage for safety
  initial_margin: 35.0  # USDT - small amount for testing
  dry_run: false  # Set to true for testing without real trades

# Grid Strategy Configuration
grid:
  type: "neutral"  # neutral, long, short - using neutral to avoid initial market buy
  calculation: "arithmetic"  # arithmetic, geometric
  price_range:
    lower: 0.5174  # Lower bound of trading range
    upper: 0.6972  # Upper bound of trading range
  grid_count: 5  # Very small grid count for testing

# Monitoring Configuration
monitoring:
  log_level: "info"  # debug, info, warn, error, fatal
  metrics_enabled: true
  trade_history: true
  update_interval: 5s  # Faster updates for testing
  # log_file: "gridbot.log"  # Optional log file
  # webhook_url: "https://hooks.slack.com/..."  # Optional webhook for notifications

# Advanced Configuration (Optional)
advanced:
  order_timeout: 30s
  max_retries: 3
  retry_delay: 5s
  price_deviation: 0.1  # Allow 0.1% price deviation
  min_order_size: 0.001  # Minimum order size
  rebalance_interval: 5m  # Grid rebalancing interval
  health_check_interval: 30s

  # WebSocket Configuration
  websocket:
    reconnect_interval: 5s
    ping_interval: 20s
    max_reconnects: 10
    buffer_size: 1000
