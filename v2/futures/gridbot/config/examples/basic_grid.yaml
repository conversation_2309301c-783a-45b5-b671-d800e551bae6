# Basic Grid Trading Configuration
# This is a simple configuration for beginners

# API Configuration
api:
  api_key: "uqmdgwUnig4OZabB3BWWY3IaKuyf6y1izlMU8Qz7RpBkJ0LP8FHoEdmYw3QnewmP"  # Will be overridden by BINANCE_API_KEY environment variable
  secret_key: "rom5DKGjk6IXfjL0a3m9S2wJDVuOnscz5ndyjK2AYRjoDpuoEtO89GCXS0JrRWxi"  # Will be overridden by BINANCE_SECRET_KEY environment variable
  testnet: false  # Set to false for live API but using dry_run for safety
  # proxy: "http://127.0.0.1:7890"  # Optional proxy

# Trading Configuration
trading:
  symbol: "KAITOUSDC"
  leverage: 10  # Reduced from 20 to lower margin requirements
  initial_margin: 40.0  # USDT - set high to handle volatile market conditions
  dry_run: false  # Set to true for testing without real trades

# Grid Strategy Configuration
grid:
  type: "long"  # neutral, long, short
  calculation: "arithmetic"  # arithmetic, geometric
  price_range:
    lower: 1.259  # Lower bound of trading range
    upper: 1.5064  # Upper bound of trading range
  grid_count: 50  # Reduced from 29 to lower margin requirements



# Monitoring Configuration
monitoring:
  log_level: "info"  # debug, info, warn, error, fatal
  metrics_enabled: true
  trade_history: true
  update_interval: 10s  # Status update interval
  # log_file: "gridbot.log"  # Optional log file
  # webhook_url: "https://hooks.slack.com/..."  # Optional webhook for notifications

# Advanced Configuration (Optional)
advanced:
  order_timeout: 30s
  max_retries: 3
  retry_delay: 5s
  price_deviation: 0.1  # Allow 0.1% price deviation
  min_order_size: 0.001  # Minimum order size
  rebalance_interval: 5m  # Grid rebalancing interval
  health_check_interval: 30s

  # WebSocket Configuration
  websocket:
    reconnect_interval: 5s
    ping_interval: 20s
    max_reconnects: 10
    buffer_size: 1000
