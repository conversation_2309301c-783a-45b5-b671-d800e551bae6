# Neutral Grid Strategy Test Configuration
# Tests equal distribution of buy/sell orders around mid-price

# API Configuration - Binance Testnet
api:
  api_key: "aab8ef750fcc27743c62c613f4089a8c0a47f083cd15cf4b0c0843faa6efb798"
  secret_key: "11f3f1ffcd5ef5069bef8ab5ee786572420213ca4d657cfec5b1603fb21406ca"
  testnet: true
  # proxy: ""

# Trading Configuration
trading:
  symbol: "BTCUSDT"
  leverage: 2  # Lower leverage to reduce margin requirements
  initial_margin: 25000.0  # Sufficient margin for comprehensive testing
  dry_run: true  # Safety first - no real orders

# Grid Strategy Configuration - NEUTRAL
grid:
  type: "neutral"  # Equal buy/sell distribution around mid-price
  calculation: "arithmetic"  # Equal price spacing
  price_range:
    lower: 117000.0  # Smaller range around current BTC price
    upper: 119000.0  # Smaller range for testing
  grid_count: 6  # Fewer grids for lower margin requirements



# Monitoring Configuration
monitoring:
  log_level: "debug"  # Detailed logging for testing
  log_file: "neutral_grid_test.log"
  metrics_enabled: true
  trade_history: true
  update_interval: 3s  # Frequent updates for testing
  # webhook_url: ""  # Add webhook if needed

# Advanced Configuration
advanced:
  order_timeout: 45s
  max_retries: 3
  retry_delay: 5s
  price_deviation: 0.15  # Allow 0.15% price deviation
  min_order_size: 0.001  # Minimum order size
  max_order_size: 0.05  # Maximum order size for testing
  rebalance_interval: 3m  # Frequent rebalancing for testing
  health_check_interval: 30s

  # WebSocket Configuration
  websocket:
    reconnect_interval: 5s
    ping_interval: 20s
    max_reconnects: 10
    buffer_size: 1000

# Test-specific comments:
# - Mid-price should be around 118,000 USDT
# - Expect ~5 buy orders below mid-price (115k-118k range)
# - Expect ~5 sell orders above mid-price (118k-121k range)
# - Order quantities should be roughly equal for buy/sell
# - Strategy should be market-neutral
