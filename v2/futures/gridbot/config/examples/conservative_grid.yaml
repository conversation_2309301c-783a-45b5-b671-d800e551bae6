# Conservative Grid Trading Configuration
# This configuration prioritizes safety and risk management

# API Configuration
api:
  api_key: "your_binance_api_key_here"
  secret_key: "your_binance_secret_key_here"
  testnet: true  # Always test first
  # proxy: ""  # No proxy needed

# Trading Configuration
trading:
  symbol: "BTCUSDT"
  leverage: 2  # Low leverage for safety
  initial_margin: 500.0  # USDT
  dry_run: true  # Start with dry run

# Grid Strategy Configuration
grid:
  type: "neutral"  # Neutral strategy for ranging markets
  calculation: "arithmetic"  # Simple arithmetic progression
  price_range:
    lower: 25000.0  # Conservative range
    upper: 30000.0
  grid_count: 15  # Moderate number of grids


# Monitoring Configuration
monitoring:
  log_level: "info"
  log_file: "gridbot_conservative.log"
  metrics_enabled: true
  trade_history: true
  update_interval: 30s  # Less frequent updates to reduce noise
  # webhook_url: ""  # No webhook for conservative approach

# Advanced Configuration
advanced:
  order_timeout: 60s  # Longer timeout for careful execution
  max_retries: 2  # Fewer retries to avoid aggressive trading
  retry_delay: 10s  # Longer delay between retries
  price_deviation: 0.2  # More tolerant price deviation
  min_order_size: 0.001  # Small order sizes
  max_order_size: 0.1  # Cap order sizes for safety
  rebalance_interval: 10m  # Less frequent rebalancing
  health_check_interval: 60s  # Less frequent health checks

  # WebSocket Configuration
  websocket:
    reconnect_interval: 10s  # Slower reconnection
    ping_interval: 30s  # Less frequent pings
    max_reconnects: 5  # Fewer reconnection attempts
    buffer_size: 500  # Smaller buffer
