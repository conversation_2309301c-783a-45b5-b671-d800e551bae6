package orders

import (
	"context"
	"fmt"
	"strconv"
	"sync"
	"time"

	"github.com/adshao/go-binance/v2/futures"
	"github.com/adshao/go-binance/v2/futures/gridbot/config"
	"github.com/adshao/go-binance/v2/futures/gridbot/market"
)

// OrderManagerImpl implements the OrderManagerInterface
type OrderManagerImpl struct {
	client       *futures.Client
	config       *config.Config
	activeOrders map[int64]*ManagedOrder
	orderHistory []*ManagedOrder
	statistics   *OrderStatistics
	
	// Synchronization
	mu sync.RWMutex
	
	// Channels
	orderEventChan chan *OrderEvent
	stopChan       chan struct{}
	
	// State
	running bool
	
	// Order ID counter for client order IDs
	orderIDCounter int64
}

// NewOrderManager creates a new order manager
func NewOrderManager(client *futures.Client, cfg *config.Config) *OrderManagerImpl {
	return &OrderManagerImpl{
		client:         client,
		config:         cfg,
		activeOrders:   make(map[int64]*ManagedOrder),
		orderHistory:   make([]*ManagedOrder, 0),
		statistics:     &OrderStatistics{CommonErrors: make(map[string]int)},
		orderEventChan: make(chan *OrderEvent, 1000),
		stopChan:       make(chan struct{}),
		running:        false,
		orderIDCounter: time.Now().Unix(),
	}
}

// Start starts the order manager
func (om *OrderManagerImpl) Start() error {
	om.mu.Lock()
	defer om.mu.Unlock()
	
	if om.running {
		return fmt.Errorf("order manager is already running")
	}
	
	om.running = true
	
	// Start background goroutines
	go om.orderSyncWorker()
	go om.statisticsWorker()
	
	om.sendEvent(OrderEventType("manager_started"), nil, "Order manager started", nil)
	
	return nil
}

// Stop stops the order manager
func (om *OrderManagerImpl) Stop() error {
	om.mu.Lock()
	defer om.mu.Unlock()
	
	if !om.running {
		return nil
	}
	
	om.running = false
	
	// Signal stop to background workers
	close(om.stopChan)
	
	om.sendEvent(OrderEventType("manager_stopped"), nil, "Order manager stopped", nil)
	
	return nil
}

// IsRunning returns whether the order manager is running
func (om *OrderManagerImpl) IsRunning() bool {
	om.mu.RLock()
	defer om.mu.RUnlock()
	return om.running
}

// PlaceOrder places a new order
func (om *OrderManagerImpl) PlaceOrder(request *PlaceOrderRequest) (*PlaceOrderResponse, error) {
	om.mu.Lock()
	defer om.mu.Unlock()
	
	// Create managed order
	order := om.createManagedOrder(request)
	
	// Place order with Binance
	response, err := om.placeOrderWithBinance(order)
	if err != nil {
		order.Status = OrderStatusError
		order.LastError = err.Error()
		om.sendEvent(OrderEventError, order, "Failed to place order", err)
		return &PlaceOrderResponse{Success: false, Error: err}, err
	}
	
	// Update order with response
	om.updateOrderFromResponse(order, response)
	order.Status = OrderStatusPlaced
	
	// Add to active orders
	om.activeOrders[order.ID] = order
	
	// Update statistics
	om.statistics.TotalOrders++
	om.statistics.ActiveOrders++
	
	om.sendEvent(OrderEventPlaced, order, "Order placed successfully", nil)
	
	return &PlaceOrderResponse{
		Success:         true,
		Order:           order,
		BinanceResponse: response,
	}, nil
}

// PlaceOrderAsync places an order asynchronously
func (om *OrderManagerImpl) PlaceOrderAsync(request *PlaceOrderRequest) error {
	go func() {
		_, err := om.PlaceOrder(request)
		if err != nil {
			// Error is already handled in PlaceOrder
			return
		}
	}()
	return nil
}

// CancelOrder cancels an existing order
func (om *OrderManagerImpl) CancelOrder(request *CancelOrderRequest) (*CancelOrderResponse, error) {
	om.mu.Lock()
	defer om.mu.Unlock()
	
	// Find the order
	var order *ManagedOrder
	if request.OrderID != 0 {
		order = om.activeOrders[request.OrderID]
	} else if request.ClientOrderID != "" {
		for _, o := range om.activeOrders {
			if o.ClientOrderID == request.ClientOrderID {
				order = o
				break
			}
		}
	}
	
	if order == nil {
		return &CancelOrderResponse{
			Success: false,
			Error:   fmt.Errorf("order not found"),
		}, nil
	}
	
	// Cancel with Binance
	err := om.cancelOrderWithBinance(order)
	if err != nil {
		order.LastError = err.Error()
		om.sendEvent(OrderEventError, order, "Failed to cancel order", err)
		return &CancelOrderResponse{Success: false, Error: err}, err
	}
	
	// Update order status
	order.Status = OrderStatusCancelled
	now := time.Now()
	order.CancelledAt = &now
	order.UpdatedAt = now
	
	// Move from active to history
	delete(om.activeOrders, order.ID)
	om.orderHistory = append(om.orderHistory, order)
	
	// Update statistics
	om.statistics.ActiveOrders--
	om.statistics.CancelledOrders++
	
	om.sendEvent(OrderEventCancelled, order, fmt.Sprintf("Order cancelled: %s", request.Reason), nil)
	
	return &CancelOrderResponse{Success: true, Order: order}, nil
}

// CancelAllOrders cancels all active orders for a symbol
func (om *OrderManagerImpl) CancelAllOrders(symbol string) error {
	om.mu.RLock()
	ordersToCancel := make([]*ManagedOrder, 0)
	for _, order := range om.activeOrders {
		if symbol == "" || order.Symbol == symbol {
			ordersToCancel = append(ordersToCancel, order)
		}
	}
	om.mu.RUnlock()
	
	var lastError error
	cancelledCount := 0
	
	for _, order := range ordersToCancel {
		request := &CancelOrderRequest{
			OrderID: order.ID,
			Symbol:  order.Symbol,
			Reason:  "Cancel all orders",
		}
		
		_, err := om.CancelOrder(request)
		if err != nil {
			lastError = err
		} else {
			cancelledCount++
		}
	}
	
	om.sendEvent(OrderEventType("bulk_cancel"), nil, 
		fmt.Sprintf("Cancelled %d orders for symbol %s", cancelledCount, symbol), lastError)
	
	return lastError
}

// GetOrder retrieves an order by ID
func (om *OrderManagerImpl) GetOrder(orderID int64) (*ManagedOrder, error) {
	om.mu.RLock()
	defer om.mu.RUnlock()
	
	// Check active orders first
	if order, exists := om.activeOrders[orderID]; exists {
		return order, nil
	}
	
	// Check order history
	for _, order := range om.orderHistory {
		if order.ID == orderID {
			return order, nil
		}
	}
	
	return nil, fmt.Errorf("order with ID %d not found", orderID)
}

// GetOrderByClientID retrieves an order by client order ID
func (om *OrderManagerImpl) GetOrderByClientID(clientOrderID string) (*ManagedOrder, error) {
	om.mu.RLock()
	defer om.mu.RUnlock()
	
	// Check active orders first
	for _, order := range om.activeOrders {
		if order.ClientOrderID == clientOrderID {
			return order, nil
		}
	}
	
	// Check order history
	for _, order := range om.orderHistory {
		if order.ClientOrderID == clientOrderID {
			return order, nil
		}
	}
	
	return nil, fmt.Errorf("order with client ID %s not found", clientOrderID)
}

// GetOrders retrieves orders based on query criteria
func (om *OrderManagerImpl) GetOrders(query *OrderQuery) ([]*ManagedOrder, error) {
	om.mu.RLock()
	defer om.mu.RUnlock()
	
	var allOrders []*ManagedOrder
	
	// Combine active orders and history
	for _, order := range om.activeOrders {
		allOrders = append(allOrders, order)
	}
	allOrders = append(allOrders, om.orderHistory...)
	
	// Apply filters
	var filteredOrders []*ManagedOrder
	for _, order := range allOrders {
		if om.matchesQuery(order, query) {
			filteredOrders = append(filteredOrders, order)
		}
	}
	
	// Apply limit
	if query.Limit > 0 && len(filteredOrders) > query.Limit {
		filteredOrders = filteredOrders[:query.Limit]
	}
	
	return filteredOrders, nil
}

// GetActiveOrders returns all active orders
func (om *OrderManagerImpl) GetActiveOrders() []*ManagedOrder {
	om.mu.RLock()
	defer om.mu.RUnlock()
	
	orders := make([]*ManagedOrder, 0, len(om.activeOrders))
	for _, order := range om.activeOrders {
		orders = append(orders, order)
	}
	
	return orders
}

// GetStatistics returns order management statistics
func (om *OrderManagerImpl) GetStatistics() *OrderStatistics {
	om.mu.RLock()
	defer om.mu.RUnlock()
	
	// Create a copy to avoid race conditions
	stats := *om.statistics
	stats.ActiveOrders = len(om.activeOrders)
	
	// Calculate derived statistics
	if stats.TotalOrders > 0 {
		stats.SuccessRate = float64(stats.FilledOrders) / float64(stats.TotalOrders) * 100
		stats.ErrorRate = float64(stats.ErrorOrders) / float64(stats.TotalOrders) * 100
		
		if stats.TotalRetries > 0 {
			stats.AvgRetries = float64(stats.TotalRetries) / float64(stats.TotalOrders)
		}
	}
	
	return &stats
}

// GetOrderEvents returns the order event channel
func (om *OrderManagerImpl) GetOrderEvents() <-chan *OrderEvent {
	return om.orderEventChan
}

// UpdateConfig updates the configuration
func (om *OrderManagerImpl) UpdateConfig(cfg *config.Config) error {
	om.mu.Lock()
	defer om.mu.Unlock()

	om.config = cfg
	return nil
}

// SyncOrder synchronizes an order with Binance
func (om *OrderManagerImpl) SyncOrder(orderID int64) error {
	om.mu.Lock()
	defer om.mu.Unlock()

	order, exists := om.activeOrders[orderID]
	if !exists {
		return fmt.Errorf("order %d not found in active orders", orderID)
	}

	// Query order status from Binance
	binanceOrder, err := om.client.NewGetOrderService().
		Symbol(order.Symbol).
		OrderID(orderID).
		Do(context.Background())
	if err != nil {
		return fmt.Errorf("failed to sync order %d: %w", orderID, err)
	}

	// Update order status based on Binance response
	om.updateOrderFromBinanceOrder(order, binanceOrder)

	return nil
}

// SyncAllOrders synchronizes all active orders
func (om *OrderManagerImpl) SyncAllOrders() error {
	om.mu.RLock()
	orderIDs := make([]int64, 0, len(om.activeOrders))
	for orderID := range om.activeOrders {
		orderIDs = append(orderIDs, orderID)
	}
	om.mu.RUnlock()

	var lastError error
	for _, orderID := range orderIDs {
		if err := om.SyncOrder(orderID); err != nil {
			lastError = err
		}
	}

	return lastError
}

// Helper methods

// createManagedOrder creates a new managed order from a request
func (om *OrderManagerImpl) createManagedOrder(request *PlaceOrderRequest) *ManagedOrder {
	om.orderIDCounter++

	now := time.Now()
	clientOrderID := fmt.Sprintf("grid_%d_%d", om.orderIDCounter, now.Unix())

	return &ManagedOrder{
		ClientOrderID: clientOrderID,
		Symbol:        request.Symbol,
		Side:          request.Side,
		Type:          request.Type,
		Quantity:      request.Quantity,
		Price:         request.Price,
		Status:        OrderStatusPending,
		GridLevelID:   request.GridLevelID,
		GridLevel:     request.GridLevel,
		CreatedAt:     now,
		UpdatedAt:     now,
		RetryCount:    0,
	}
}

// placeOrderWithBinance places an order with Binance
func (om *OrderManagerImpl) placeOrderWithBinance(order *ManagedOrder) (*futures.CreateOrderResponse, error) {
	service := om.client.NewCreateOrderService().
		Symbol(order.Symbol).
		Side(order.Side).
		Type(order.Type).
		Quantity(order.Quantity)

	if order.Price != "" {
		service = service.Price(order.Price)
	}

	// Set default time in force for limit orders
	if order.Type == futures.OrderTypeLimit {
		service = service.TimeInForce(futures.TimeInForceTypeGTC)
	}

	// Set position side for hedge mode
	service = service.PositionSide(futures.PositionSideTypeBoth)

	ctx, cancel := context.WithTimeout(context.Background(), om.config.Advanced.OrderTimeout)
	defer cancel()

	return service.Do(ctx)
}

// cancelOrderWithBinance cancels an order with Binance
func (om *OrderManagerImpl) cancelOrderWithBinance(order *ManagedOrder) error {
	ctx, cancel := context.WithTimeout(context.Background(), om.config.Advanced.OrderTimeout)
	defer cancel()

	_, err := om.client.NewCancelOrderService().
		Symbol(order.Symbol).
		OrderID(order.ID).
		Do(ctx)

	return err
}

// updateOrderFromResponse updates order from Binance create order response
func (om *OrderManagerImpl) updateOrderFromResponse(order *ManagedOrder, response *futures.CreateOrderResponse) {
	order.ID = response.OrderID
	order.BinanceResponse = response
	order.UpdatedAt = time.Now()
}

// updateOrderFromBinanceOrder updates order from Binance get order response
func (om *OrderManagerImpl) updateOrderFromBinanceOrder(order *ManagedOrder, binanceOrder *futures.Order) {
	order.ExecutedQuantity = binanceOrder.ExecutedQuantity
	order.CumulativeQuote = binanceOrder.CumQuote
	order.AvgPrice = binanceOrder.AvgPrice
	order.UpdatedAt = time.Now()

	// Update status based on Binance status
	switch binanceOrder.Status {
	case futures.OrderStatusTypeFilled:
		if order.Status != OrderStatusFilled {
			order.Status = OrderStatusFilled
			now := time.Now()
			order.FilledAt = &now

			// Move to history
			delete(om.activeOrders, order.ID)
			om.orderHistory = append(om.orderHistory, order)

			// Update statistics
			om.statistics.ActiveOrders--
			om.statistics.FilledOrders++

			om.sendEvent(OrderEventFilled, order, "Order filled", nil)
		}
	case futures.OrderStatusTypeCanceled:
		if order.Status != OrderStatusCancelled {
			order.Status = OrderStatusCancelled
			now := time.Now()
			order.CancelledAt = &now

			// Move to history
			delete(om.activeOrders, order.ID)
			om.orderHistory = append(om.orderHistory, order)

			// Update statistics
			om.statistics.ActiveOrders--
			om.statistics.CancelledOrders++

			om.sendEvent(OrderEventCancelled, order, "Order cancelled", nil)
		}
	case futures.OrderStatusTypeRejected:
		if order.Status != OrderStatusRejected {
			order.Status = OrderStatusRejected

			// Move to history
			delete(om.activeOrders, order.ID)
			om.orderHistory = append(om.orderHistory, order)

			// Update statistics
			om.statistics.ActiveOrders--
			om.statistics.RejectedOrders++

			om.sendEvent(OrderEventRejected, order, "Order rejected", nil)
		}
	}
}

// matchesQuery checks if an order matches the query criteria
func (om *OrderManagerImpl) matchesQuery(order *ManagedOrder, query *OrderQuery) bool {
	if query.Symbol != "" && order.Symbol != query.Symbol {
		return false
	}

	if query.Status != "" && order.Status != query.Status {
		return false
	}

	if query.Side != "" && order.Side != query.Side {
		return false
	}

	if query.GridLevelID != nil && order.GridLevelID != *query.GridLevelID {
		return false
	}

	if query.StartTime != nil && order.CreatedAt.Before(*query.StartTime) {
		return false
	}

	if query.EndTime != nil && order.CreatedAt.After(*query.EndTime) {
		return false
	}

	return true
}

// sendEvent sends an event to the event channel
func (om *OrderManagerImpl) sendEvent(eventType OrderEventType, order *ManagedOrder, message string, err error) {
	event := &OrderEvent{
		Type:      eventType,
		Timestamp: time.Now(),
		Order:     order,
		Message:   message,
		Error:     err,
	}

	select {
	case om.orderEventChan <- event:
		// Event sent successfully
	default:
		// Channel is full, skip event (non-blocking)
	}
}

// Background workers

// orderSyncWorker periodically syncs orders with Binance
func (om *OrderManagerImpl) orderSyncWorker() {
	ticker := time.NewTicker(30 * time.Second) // Sync every 30 seconds
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if om.IsRunning() {
				om.SyncAllOrders()
			}
		case <-om.stopChan:
			return
		}
	}
}

// statisticsWorker updates statistics periodically
func (om *OrderManagerImpl) statisticsWorker() {
	ticker := time.NewTicker(10 * time.Second) // Update stats every 10 seconds
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if om.IsRunning() {
				om.updateStatistics()
			}
		case <-om.stopChan:
			return
		}
	}
}

// updateStatistics updates internal statistics
func (om *OrderManagerImpl) updateStatistics() {
	om.mu.Lock()
	defer om.mu.Unlock()

	// Update last order time
	var lastOrderTime *time.Time
	for _, order := range om.activeOrders {
		if lastOrderTime == nil || order.CreatedAt.After(*lastOrderTime) {
			lastOrderTime = &order.CreatedAt
		}
	}
	for _, order := range om.orderHistory {
		if lastOrderTime == nil || order.CreatedAt.After(*lastOrderTime) {
			lastOrderTime = &order.CreatedAt
		}
	}
	om.statistics.LastOrderTime = lastOrderTime

	// Calculate orders per second
	if lastOrderTime != nil {
		duration := time.Since(*lastOrderTime)
		if duration > 0 {
			om.statistics.OrdersPerSecond = float64(om.statistics.TotalOrders) / duration.Seconds()
		}
	}
}

// UpdateOrderFromWebSocket updates an order based on WebSocket data
func (om *OrderManagerImpl) UpdateOrderFromWebSocket(orderData interface{}) error {
	om.mu.Lock()
	defer om.mu.Unlock()

	// Import the market package to access OrderUpdateData type
	if orderUpdate, ok := orderData.(*market.OrderUpdateData); ok {
		// Find the order by ID
		order, exists := om.activeOrders[orderUpdate.OrderID]
		if !exists {
			// Order might be in history, check there too
			for _, histOrder := range om.orderHistory {
				if histOrder.ID == orderUpdate.OrderID {
					// Order already processed, skip
					return nil
				}
			}
			// Order not found, this might be an order placed outside our system
			return fmt.Errorf("order %d not found in active orders", orderUpdate.OrderID)
		}

		// Update order fields from WebSocket data
		if orderUpdate.ExecutedQuantity != "" {
			if executedQty, err := strconv.ParseFloat(orderUpdate.ExecutedQuantity, 64); err == nil {
				order.ExecutedQuantity = fmt.Sprintf("%.8f", executedQty)
			}
		}

		// Update average price from WebSocket data
		if orderUpdate.AveragePrice != "" {
			order.AvgPrice = orderUpdate.AveragePrice
		}

		// Update order status and handle state transitions
		switch orderUpdate.Status {
		case futures.OrderStatusTypeFilled:
			if order.Status != OrderStatusFilled {
				order.Status = OrderStatusFilled
				now := time.Now()
				order.FilledAt = &now
				order.UpdatedAt = now

				// Move to history
				delete(om.activeOrders, order.ID)
				om.orderHistory = append(om.orderHistory, order)

				// Update statistics
				om.statistics.ActiveOrders--
				om.statistics.FilledOrders++

				// Send fill event
				om.sendEvent(OrderEventFilled, order, "Order filled via WebSocket", nil)
			}

		case futures.OrderStatusTypePartiallyFilled:
			// For partially filled orders, we'll keep them as "placed" status
			// and just update the executed quantity
			order.UpdatedAt = time.Now()

			// Send updated event
			om.sendEvent(OrderEventUpdated, order, "Order partially filled via WebSocket", nil)

		case futures.OrderStatusTypeCanceled:
			if order.Status != OrderStatusCancelled {
				order.Status = OrderStatusCancelled
				now := time.Now()
				order.CancelledAt = &now
				order.UpdatedAt = now

				// Move to history
				delete(om.activeOrders, order.ID)
				om.orderHistory = append(om.orderHistory, order)

				// Update statistics
				om.statistics.ActiveOrders--
				om.statistics.CancelledOrders++

				// Send cancelled event
				om.sendEvent(OrderEventCancelled, order, "Order cancelled via WebSocket", nil)
			}

		case futures.OrderStatusTypeRejected:
			if order.Status != OrderStatusRejected {
				order.Status = OrderStatusRejected
				order.UpdatedAt = time.Now()

				// Move to history
				delete(om.activeOrders, order.ID)
				om.orderHistory = append(om.orderHistory, order)

				// Update statistics
				om.statistics.ActiveOrders--
				om.statistics.RejectedOrders++

				// Send rejected event
				om.sendEvent(OrderEventRejected, order, "Order rejected via WebSocket", nil)
			}
		}

		return nil
	}

	return fmt.Errorf("invalid order data type")
}
