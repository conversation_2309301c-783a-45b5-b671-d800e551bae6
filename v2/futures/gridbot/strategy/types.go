package strategy

import (
	"time"

	"github.com/adshao/go-binance/v2/futures"
	"github.com/adshao/go-binance/v2/futures/gridbot/config"
)

// GridStrategy represents the main grid trading strategy
type GridStrategy struct {
	Config     *config.Config
	Levels     []*GridLevel
	Statistics *StrategyStatistics
	Status     StrategyStatus
	CreatedAt  time.Time
	UpdatedAt  time.Time
}

// GridLevel represents a single level in the grid
type GridLevel struct {
	ID        int                     `json:"id"`
	Price     float64                 `json:"price"`
	Side      futures.SideType        `json:"side"`
	Quantity  float64                 `json:"quantity"`
	OrderID   int64                   `json:"order_id,omitempty"`
	Status    config.GridLevelStatus  `json:"status"`
	CreatedAt time.Time               `json:"created_at"`
	UpdatedAt time.Time               `json:"updated_at"`
	FilledAt  *time.Time              `json:"filled_at,omitempty"`

	// Order details
	OriginalQuantity string `json:"original_quantity,omitempty"`
	ExecutedQuantity string `json:"executed_quantity,omitempty"`
	CumulativeQuote  string `json:"cumulative_quote,omitempty"`
	Commission       string `json:"commission,omitempty"`
	CommissionAsset  string `json:"commission_asset,omitempty"`

	// Grid regeneration tracking
	OriginalLevelID    *int    `json:"original_level_id,omitempty"`    // Points to original grid level if this is regenerated
	RegeneratedOrderID *int64  `json:"regenerated_order_id,omitempty"` // Points to regenerated order ID
	PairOrderID        *int64  `json:"pair_order_id,omitempty"`        // Points to the paired order (buy-sell pair)
	CycleID            *string `json:"cycle_id,omitempty"`             // Unique ID for the grid cycle
	IsRegenerated      bool    `json:"is_regenerated"`                 // True if this is a regenerated order

	// Fill details for profit calculation
	FillPrice    *float64 `json:"fill_price,omitempty"`    // Actual fill price
	FillQuantity *float64 `json:"fill_quantity,omitempty"` // Actual fill quantity
}

// StrategyStatus represents the current status of the strategy
type StrategyStatus string

const (
	StrategyStatusInitializing StrategyStatus = "initializing"
	StrategyStatusActive       StrategyStatus = "active"
	StrategyStatusPaused       StrategyStatus = "paused"
	StrategyStatusStopped      StrategyStatus = "stopped"
	StrategyStatusError        StrategyStatus = "error"
	StrategyStatusCompleted    StrategyStatus = "completed"
)

// StrategyStatistics contains performance metrics for the strategy
type StrategyStatistics struct {
	StartTime         time.Time `json:"start_time"`
	EndTime           *time.Time `json:"end_time,omitempty"`
	RunDuration       time.Duration `json:"run_duration"`
	
	// Trading statistics
	TotalTrades       int     `json:"total_trades"`
	BuyTrades         int     `json:"buy_trades"`
	SellTrades        int     `json:"sell_trades"`
	ProfitableTrades  int     `json:"profitable_trades"`
	LosingTrades      int     `json:"losing_trades"`
	
	// Financial statistics
	TotalVolume       float64 `json:"total_volume"`
	TotalPnL          float64 `json:"total_pnl"`
	RealizedPnL       float64 `json:"realized_pnl"`
	UnrealizedPnL     float64 `json:"unrealized_pnl"`
	TotalFees         float64 `json:"total_fees"`
	NetPnL            float64 `json:"net_pnl"`
	ROI               float64 `json:"roi"`
	
	// Risk statistics
	MaxDrawdown       float64 `json:"max_drawdown"`
	MaxDrawdownDate   *time.Time `json:"max_drawdown_date,omitempty"`
	CurrentDrawdown   float64 `json:"current_drawdown"`
	WinRate           float64 `json:"win_rate"`
	ProfitFactor      float64 `json:"profit_factor"`
	SharpeRatio       float64 `json:"sharpe_ratio"`
	
	// Grid statistics
	GridEfficiency    float64 `json:"grid_efficiency"`
	ActiveGrids       int     `json:"active_grids"`
	FilledGrids       int     `json:"filled_grids"`
	AvgTradeSize      float64 `json:"avg_trade_size"`
	AvgHoldTime       time.Duration `json:"avg_hold_time"`
	
	// Price statistics
	HighestPrice      float64 `json:"highest_price"`
	LowestPrice       float64 `json:"lowest_price"`
	CurrentPrice      float64 `json:"current_price"`
	PriceRange        float64 `json:"price_range"`
	
	// Performance metrics
	TradesPerHour     float64 `json:"trades_per_hour"`
	VolumePerHour     float64 `json:"volume_per_hour"`
	LastTradeTime     *time.Time `json:"last_trade_time,omitempty"`

	// Grid cycle statistics
	CompletedCycles   int     `json:"completed_cycles"`
	TotalCycleProfit  float64 `json:"total_cycle_profit"`
	AvgCycleProfit    float64 `json:"avg_cycle_profit"`
	AvgCycleDuration  time.Duration `json:"avg_cycle_duration"`
	BestCycleProfit   float64 `json:"best_cycle_profit"`
	WorstCycleProfit  float64 `json:"worst_cycle_profit"`
	RegeneratedOrders int     `json:"regenerated_orders"`
}

// GridCalculationResult contains the result of grid calculation
type GridCalculationResult struct {
	Levels        []*GridLevel `json:"levels"`
	TotalQuantity float64      `json:"total_quantity"`
	RequiredMargin float64     `json:"required_margin"`
	GridSpacing   float64      `json:"grid_spacing"`
	PriceRange    float64      `json:"price_range"`
}

// OrderPlacementRequest represents a request to place an order
type OrderPlacementRequest struct {
	GridLevel    *GridLevel
	Symbol       string
	Side         futures.SideType
	OrderType    futures.OrderType
	Quantity     string
	Price        string
	TimeInForce  futures.TimeInForceType
	PositionSide futures.PositionSideType
	ReduceOnly   bool
}

// OrderPlacementResult represents the result of placing an order
type OrderPlacementResult struct {
	Success   bool
	OrderID   int64
	Error     error
	GridLevel *GridLevel
	Response  *futures.CreateOrderResponse
}

// GridRebalanceRequest represents a request to rebalance the grid
type GridRebalanceRequest struct {
	CurrentPrice  float64
	ForceRebalance bool
	Reason        string
}

// GridRebalanceResult represents the result of grid rebalancing
type GridRebalanceResult struct {
	Success         bool
	OrdersPlaced    int
	OrdersCancelled int
	NewLevels       []*GridLevel
	Error           error
	Details         string
}



// TrendDirection represents market trend direction
type TrendDirection string

const (
	TrendUp      TrendDirection = "up"
	TrendDown    TrendDirection = "down"
	TrendSideways TrendDirection = "sideways"
	TrendUnknown  TrendDirection = "unknown"
)

// GridEvent represents events that occur during grid trading
type GridEvent struct {
	Type      GridEventType `json:"type"`
	Timestamp time.Time     `json:"timestamp"`
	GridLevel *GridLevel    `json:"grid_level,omitempty"`
	Price     float64       `json:"price,omitempty"`
	Message   string        `json:"message"`
	Data      interface{}   `json:"data,omitempty"`
}

// GridEventType represents different types of grid events
type GridEventType string

const (
	GridEventOrderPlaced    GridEventType = "order_placed"
	GridEventOrderFilled    GridEventType = "order_filled"
	GridEventOrderCancelled GridEventType = "order_cancelled"
	GridEventOrderError     GridEventType = "order_error"
	GridEventGridRebalanced GridEventType = "grid_rebalanced"
	GridEventPriceOutOfRange GridEventType = "price_out_of_range"
	GridEventRiskTriggered  GridEventType = "risk_triggered"
	GridEventStrategyPaused GridEventType = "strategy_paused"
	GridEventStrategyResumed GridEventType = "strategy_resumed"
	GridEventStrategyStopped GridEventType = "strategy_stopped"
)

// GridCompositionAnalysis contains analysis of grid order composition
type GridCompositionAnalysis struct {
	// Order counts
	BuyOrderCount  int `json:"buy_order_count"`
	SellOrderCount int `json:"sell_order_count"`
	TotalOrders    int `json:"total_orders"`

	// Order percentages
	BuyOrderPercent  float64 `json:"buy_order_percent"`
	SellOrderPercent float64 `json:"sell_order_percent"`

	// Quantities
	TotalBuyQuantity  float64 `json:"total_buy_quantity"`
	TotalSellQuantity float64 `json:"total_sell_quantity"`
	TotalQuantity     float64 `json:"total_quantity"`

	// Quantity percentages
	BuyQuantityPercent  float64 `json:"buy_quantity_percent"`
	SellQuantityPercent float64 `json:"sell_quantity_percent"`

	// Market position analysis
	MarketPositionPercent float64 `json:"market_position_percent"` // 0.0 = at lower bound, 1.0 = at upper bound

	// Margin analysis
	TotalBuyMargin  float64 `json:"total_buy_margin"`
	TotalSellMargin float64 `json:"total_sell_margin"`
	TotalMargin     float64 `json:"total_margin"`

	// Risk metrics
	BuyOrdersBelowMarket int     `json:"buy_orders_below_market"`
	SellOrdersAboveMarket int    `json:"sell_orders_above_market"`
	AvgBuyPrice          float64 `json:"avg_buy_price"`
	AvgSellPrice         float64 `json:"avg_sell_price"`
}

// GridCycle represents a completed buy-sell cycle in the grid
type GridCycle struct {
	ID           string    `json:"id"`
	BuyOrderID   int64     `json:"buy_order_id"`
	SellOrderID  int64     `json:"sell_order_id"`
	BuyPrice     float64   `json:"buy_price"`
	SellPrice    float64   `json:"sell_price"`
	Quantity     float64   `json:"quantity"`
	Profit       float64   `json:"profit"`        // Profit in quote currency (USDT)
	ProfitPercent float64  `json:"profit_percent"` // Profit as percentage
	Commission   float64   `json:"commission"`    // Total commission paid
	NetProfit    float64   `json:"net_profit"`    // Profit after commission
	CreatedAt    time.Time `json:"created_at"`
	CompletedAt  time.Time `json:"completed_at"`
	Duration     time.Duration `json:"duration"`  // Time from buy to sell
}

// GridRegenerationRequest represents a request to regenerate an order
type GridRegenerationRequest struct {
	FilledLevel    *GridLevel `json:"filled_level"`
	FillPrice      float64    `json:"fill_price"`
	FillQuantity   float64    `json:"fill_quantity"`
	CurrentPrice   float64    `json:"current_price"`
	GridSpacing    float64    `json:"grid_spacing"`
}

// GridRegenerationResult represents the result of order regeneration
type GridRegenerationResult struct {
	Success          bool       `json:"success"`
	RegeneratedLevel *GridLevel `json:"regenerated_level,omitempty"`
	OrderID          int64      `json:"order_id,omitempty"`
	CycleCompleted   bool       `json:"cycle_completed"`
	CompletedCycle   *GridCycle `json:"completed_cycle,omitempty"`
	Error            error      `json:"error,omitempty"`
}

// StrategyInterface defines the interface for grid strategies
type StrategyInterface interface {
	// Initialize the strategy
	Initialize() error
	
	// Start the strategy
	Start() error
	
	// Stop the strategy
	Stop() error
	
	// Pause the strategy
	Pause() error
	
	// Resume the strategy
	Resume() error
	
	// Update strategy with new market data
	Update(condition interface{}) error
	
	// Get current strategy status
	GetStatus() StrategyStatus
	
	// Get strategy statistics
	GetStatistics() *StrategyStatistics
	
	// Get grid levels
	GetGridLevels() []*GridLevel
	
	// Rebalance grid
	Rebalance(request *GridRebalanceRequest) (*GridRebalanceResult, error)
	
	// Handle order fill event
	HandleOrderFill(orderID int64, fillPrice float64, fillQuantity float64) error

	// Grid cycle management
	GetCompletedCycles() map[string]*GridCycle
	GetPendingCycles() map[int64]*GridLevel
	GetCycleStatistics() map[string]interface{}
	
	// Calculate required margin
	CalculateRequiredMargin() (float64, error)
	
	// Validate strategy configuration
	Validate() error
}
