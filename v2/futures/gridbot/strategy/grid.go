package strategy

import (
	"fmt"
	"strconv"
	"sync"
	"time"

	"github.com/adshao/go-binance/v2/futures"
	"github.com/adshao/go-binance/v2/futures/gridbot/config"
	"github.com/adshao/go-binance/v2/futures/gridbot/market"
	"github.com/adshao/go-binance/v2/futures/gridbot/orders"
	"github.com/adshao/go-binance/v2/futures/gridbot/precision"
)

// GridStrategyImpl implements the main grid trading strategy
type GridStrategyImpl struct {
	*GridStrategy // Embed the base struct from types.go

	calculator       *GridCalculator
	orderManager     orders.OrderManagerInterface
	precisionManager *precision.PrecisionManager

	// Synchronization
	mu sync.RWMutex

	// Channels for communication
	eventChan   chan *GridEvent
	stopChan    chan struct{}

	// Current market condition
	currentCondition *market.MarketCondition

	// Order management
	activeOrders map[int64]*GridLevel
	orderHistory []*GridLevel

	// Grid cycle tracking
	completedCycles map[string]*GridCycle
	pendingCycles   map[int64]*GridLevel // Maps order ID to its potential pair

	// Additional fields for implementation
	startedAt   *time.Time
	stoppedAt   *time.Time

	// Track if initial orders have been placed
	initialOrdersPlaced bool
}

// NewGridStrategy creates a new grid trading strategy
func NewGridStrategy(cfg *config.Config) *GridStrategyImpl {
	now := time.Now()

	baseStrategy := &GridStrategy{
		Config:     cfg,
		Levels:     make([]*GridLevel, 0),
		Statistics: &StrategyStatistics{},
		Status:     StrategyStatusInitializing,
		CreatedAt:  now,
		UpdatedAt:  now,
	}

	return &GridStrategyImpl{
		GridStrategy:        baseStrategy,
		calculator:          NewGridCalculator(cfg),
		eventChan:           make(chan *GridEvent, 100),
		stopChan:            make(chan struct{}),
		activeOrders:        make(map[int64]*GridLevel),
		orderHistory:        make([]*GridLevel, 0),
		completedCycles:     make(map[string]*GridCycle),
		pendingCycles:       make(map[int64]*GridLevel),
		initialOrdersPlaced: false,
	}
}

// SetOrderManager sets the order manager for the strategy
func (gs *GridStrategyImpl) SetOrderManager(orderManager orders.OrderManagerInterface) {
	gs.mu.Lock()
	defer gs.mu.Unlock()
	gs.orderManager = orderManager
}

// SetPrecisionManager sets the precision manager for the strategy
func (gs *GridStrategyImpl) SetPrecisionManager(pm *precision.PrecisionManager) {
	gs.mu.Lock()
	defer gs.mu.Unlock()
	gs.precisionManager = pm
}

// Initialize initializes the grid strategy
func (gs *GridStrategyImpl) Initialize() error {
	gs.mu.Lock()
	defer gs.mu.Unlock()

	// Validate configuration
	if err := gs.calculator.ValidateGridConfiguration(); err != nil {
		return fmt.Errorf("configuration validation failed: %w", err)
	}

	// Calculate grid levels
	result, err := gs.calculator.CalculateGridLevels()
	if err != nil {
		return fmt.Errorf("failed to calculate grid levels: %w", err)
	}

	gs.Levels = result.Levels

	// Initialize statistics
	gs.Statistics = &StrategyStatistics{
		StartTime:      time.Now(),
		ActiveGrids:    len(gs.Levels),
		TotalTrades:    0,
		TotalPnL:       0,
		NetPnL:         0,
		MaxDrawdown:    0,
		CurrentDrawdown: 0,
		WinRate:        0,
		GridEfficiency: 0,
	}

	gs.Status = StrategyStatusActive
	gs.UpdatedAt = time.Now()

	// Send initialization event
	gs.sendEvent(GridEventType("strategy_initialized"), nil, 0, "Grid strategy initialized successfully")

	return nil
}

// Start starts the grid strategy
func (gs *GridStrategyImpl) Start() error {
	gs.mu.Lock()
	defer gs.mu.Unlock()

	if gs.Status != StrategyStatusActive && gs.Status != StrategyStatusPaused {
		return fmt.Errorf("cannot start strategy in status: %s", gs.Status)
	}

	now := time.Now()
	gs.startedAt = &now
	gs.Status = StrategyStatusActive
	gs.Statistics.StartTime = now

	// Send start event
	gs.sendEvent(GridEventType("strategy_started"), nil, 0, "Grid strategy started")

	return nil
}

// Stop stops the grid strategy
func (gs *GridStrategyImpl) Stop() error {
	gs.mu.Lock()
	defer gs.mu.Unlock()

	if gs.Status == StrategyStatusStopped {
		return nil // Already stopped
	}

	now := time.Now()
	gs.stoppedAt = &now
	gs.Status = StrategyStatusStopped

	if gs.Statistics.EndTime == nil {
		gs.Statistics.EndTime = &now
		gs.Statistics.RunDuration = now.Sub(gs.Statistics.StartTime)
	}

	// Close stop channel to signal shutdown
	select {
	case <-gs.stopChan:
		// Already closed
	default:
		close(gs.stopChan)
	}

	// Send stop event
	gs.sendEvent(GridEventType("strategy_stopped"), nil, 0, "Grid strategy stopped")

	return nil
}

// Pause pauses the grid strategy
func (gs *GridStrategyImpl) Pause() error {
	gs.mu.Lock()
	defer gs.mu.Unlock()

	if gs.Status != StrategyStatusActive {
		return fmt.Errorf("cannot pause strategy in status: %s", gs.Status)
	}

	gs.Status = StrategyStatusPaused
	gs.UpdatedAt = time.Now()

	// Send pause event
	gs.sendEvent(GridEventStrategyPaused, nil, 0, "Grid strategy paused")

	return nil
}

// Resume resumes the grid strategy
func (gs *GridStrategyImpl) Resume() error {
	gs.mu.Lock()
	defer gs.mu.Unlock()

	if gs.Status != StrategyStatusPaused {
		return fmt.Errorf("cannot resume strategy in status: %s", gs.Status)
	}

	gs.Status = StrategyStatusActive
	gs.UpdatedAt = time.Now()

	// Send resume event
	gs.sendEvent(GridEventStrategyResumed, nil, 0, "Grid strategy resumed")

	return nil
}

// Update updates the strategy with new market data
func (gs *GridStrategyImpl) Update(condition interface{}) error {
	// Type assert to market.MarketCondition
	marketCondition, ok := condition.(*market.MarketCondition)
	if !ok {
		return fmt.Errorf("invalid condition type")
	}
	gs.mu.Lock()
	defer gs.mu.Unlock()

	if gs.Status != StrategyStatusActive {
		return nil // Skip updates when not active
	}

	gs.currentCondition = marketCondition
	gs.UpdatedAt = time.Now()

	// Update statistics with current price
	gs.updatePriceStatistics(marketCondition.Price)

	// Check if price is within grid range
	if !gs.isPriceInRange(marketCondition.Price) {
		gs.sendEvent(GridEventPriceOutOfRange, nil, marketCondition.Price,
			fmt.Sprintf("Price %.2f is outside grid range [%.2f, %.2f]",
				marketCondition.Price, gs.Config.Grid.PriceRange.Lower, gs.Config.Grid.PriceRange.Upper))
		return nil
	}

	// Place initial grid orders if not done yet
	if !gs.initialOrdersPlaced && gs.orderManager != nil {
		// For long grid strategy, place initial market buy order first
		if gs.Config.Grid.Type == config.GridTypeLong {
			if err := gs.placeInitialMarketBuyOrder(marketCondition.Price); err != nil {
				return fmt.Errorf("failed to place initial market buy order: %w", err)
			}
		}

		if err := gs.placeInitialGridOrders(); err != nil {
			return fmt.Errorf("failed to place initial grid orders: %w", err)
		}
		gs.initialOrdersPlaced = true
	}

	return nil
}

// GetStatus returns the current strategy status
func (gs *GridStrategyImpl) GetStatus() StrategyStatus {
	gs.mu.RLock()
	defer gs.mu.RUnlock()
	return gs.Status
}

// GetStatistics returns the current strategy statistics
func (gs *GridStrategyImpl) GetStatistics() *StrategyStatistics {
	gs.mu.RLock()
	defer gs.mu.RUnlock()

	// Update runtime statistics
	stats := *gs.Statistics
	if gs.startedAt != nil {
		if gs.stoppedAt != nil {
			stats.RunDuration = gs.stoppedAt.Sub(*gs.startedAt)
		} else {
			stats.RunDuration = time.Since(*gs.startedAt)
		}
	}
	
	// Calculate derived statistics
	if stats.TotalTrades > 0 {
		stats.WinRate = float64(stats.ProfitableTrades) / float64(stats.TotalTrades) * 100
		stats.AvgTradeSize = stats.TotalVolume / float64(stats.TotalTrades)
		
		if stats.RunDuration > 0 {
			hours := stats.RunDuration.Hours()
			stats.TradesPerHour = float64(stats.TotalTrades) / hours
			stats.VolumePerHour = stats.TotalVolume / hours
		}
	}
	
	// Calculate ROI
	if gs.Config.Trading.InitialMargin > 0 {
		stats.ROI = (stats.NetPnL / gs.Config.Trading.InitialMargin) * 100
	}

	// Calculate grid efficiency
	if len(gs.Levels) > 0 {
		stats.GridEfficiency = float64(stats.FilledGrids) / float64(len(gs.Levels)) * 100
	}

	return &stats
}

// GetGridLevels returns the current grid levels
func (gs *GridStrategyImpl) GetGridLevels() []*GridLevel {
	gs.mu.RLock()
	defer gs.mu.RUnlock()

	// Return a copy to prevent external modification
	levels := make([]*GridLevel, len(gs.Levels))
	copy(levels, gs.Levels)
	return levels
}

// Validate validates the strategy configuration
func (gs *GridStrategyImpl) Validate() error {
	return gs.calculator.ValidateGridConfiguration()
}

// Helper methods

// sendEvent sends an event to the event channel
func (gs *GridStrategyImpl) sendEvent(eventType GridEventType, level *GridLevel, price float64, message string) {
	event := &GridEvent{
		Type:      eventType,
		Timestamp: time.Now(),
		GridLevel: level,
		Price:     price,
		Message:   message,
	}

	select {
	case gs.eventChan <- event:
		// Event sent successfully
	default:
		// Channel is full, skip event (non-blocking)
	}
}

// isPriceInRange checks if the price is within the grid range
func (gs *GridStrategyImpl) isPriceInRange(price float64) bool {
	return price >= gs.Config.Grid.PriceRange.Lower && price <= gs.Config.Grid.PriceRange.Upper
}

// updatePriceStatistics updates price-related statistics
func (gs *GridStrategyImpl) updatePriceStatistics(price float64) {
	if gs.Statistics.HighestPrice == 0 || price > gs.Statistics.HighestPrice {
		gs.Statistics.HighestPrice = price
	}

	if gs.Statistics.LowestPrice == 0 || price < gs.Statistics.LowestPrice {
		gs.Statistics.LowestPrice = price
	}

	gs.Statistics.CurrentPrice = price
	gs.Statistics.PriceRange = gs.Statistics.HighestPrice - gs.Statistics.LowestPrice
}



// Rebalance rebalances the grid based on current market conditions
func (gs *GridStrategyImpl) Rebalance(request *GridRebalanceRequest) (*GridRebalanceResult, error) {
	gs.mu.Lock()
	defer gs.mu.Unlock()

	if gs.Status != StrategyStatusActive {
		return &GridRebalanceResult{
			Success: false,
			Error:   fmt.Errorf("cannot rebalance strategy in status: %s", gs.Status),
		}, nil
	}

	// For now, return a simple success response
	// In a full implementation, this would cancel/replace orders as needed
	return &GridRebalanceResult{
		Success:         true,
		OrdersPlaced:    0,
		OrdersCancelled: 0,
		NewLevels:       gs.Levels,
		Details:         "Rebalance completed successfully",
	}, nil
}

// HandleOrderFill handles order fill events
func (gs *GridStrategyImpl) HandleOrderFill(orderID int64, fillPrice float64, fillQuantity float64) error {
	gs.mu.Lock()
	defer gs.mu.Unlock()

	// Find the grid level for this order
	var filledLevel *GridLevel
	for _, level := range gs.Levels {
		if level.OrderID == orderID {
			level.Status = config.GridLevelStatusFilled
			level.UpdatedAt = time.Now()
			now := time.Now()
			level.FilledAt = &now

			// Store fill details for profit calculation
			level.FillPrice = &fillPrice
			level.FillQuantity = &fillQuantity

			// Update statistics
			gs.Statistics.TotalTrades++
			gs.Statistics.FilledGrids++

			filledLevel = level
			break
		}
	}

	if filledLevel == nil {
		return fmt.Errorf("order %d not found in grid levels", orderID)
	}

	// Skip regeneration for initial market buy order (ID = -1)
	if filledLevel.ID == -1 {
		fmt.Printf("✅ Initial market buy order filled: %.0f at %.7f\n", fillQuantity, fillPrice)
		return nil
	}

	// Skip regeneration in dry run mode
	if gs.Config.Trading.DryRun {
		fmt.Printf("⚠️  Dry run mode: Order %d filled but regeneration skipped\n", orderID)
		return nil
	}

	// Calculate grid spacing for regeneration
	gridSpacing := gs.calculator.CalculateGridSpacing()

	// Create regeneration request
	regenerationRequest := &GridRegenerationRequest{
		FilledLevel:  filledLevel,
		FillPrice:    fillPrice,
		FillQuantity: fillQuantity,
		CurrentPrice: fillPrice, // Use fill price as current price approximation
		GridSpacing:  gridSpacing,
	}

	// Attempt to regenerate order
	result, err := gs.regenerateGridOrder(regenerationRequest)
	if err != nil {
		fmt.Printf("❌ Failed to regenerate order for level %d: %v\n", filledLevel.ID, err)
		gs.sendEvent(GridEventType("regeneration_error"), filledLevel, fillPrice,
			fmt.Sprintf("Failed to regenerate order: %v", err))
		return err
	}

	if !result.Success {
		fmt.Printf("❌ Order regeneration failed for level %d: %v\n", filledLevel.ID, result.Error)
		gs.sendEvent(GridEventType("regeneration_failed"), filledLevel, fillPrice,
			fmt.Sprintf("Order regeneration failed: %v", result.Error))
		return result.Error
	}

	// Log success
	if result.CycleCompleted {
		fmt.Printf("🎯 Grid cycle completed with profit: %.4f USDT\n", result.CompletedCycle.NetProfit)
		gs.sendEvent(GridEventType("cycle_completed"), filledLevel, fillPrice,
			fmt.Sprintf("Grid cycle completed with profit: %.4f USDT", result.CompletedCycle.NetProfit))
	} else {
		fmt.Printf("🔄 Order regenerated successfully: %s %.0f at %.7f\n",
			result.RegeneratedLevel.Side, result.RegeneratedLevel.Quantity, result.RegeneratedLevel.Price)
		gs.sendEvent(GridEventType("order_regenerated"), result.RegeneratedLevel, result.RegeneratedLevel.Price,
			fmt.Sprintf("Regenerated %s order for %.4f at price %.7f",
				result.RegeneratedLevel.Side, result.RegeneratedLevel.Quantity, result.RegeneratedLevel.Price))
	}

	return nil
}

// CalculateRequiredMargin calculates the total margin required for the strategy
func (gs *GridStrategyImpl) CalculateRequiredMargin() (float64, error) {
	gs.mu.RLock()
	defer gs.mu.RUnlock()

	result, err := gs.calculator.CalculateGridLevels()
	if err != nil {
		return 0, fmt.Errorf("failed to calculate grid levels: %w", err)
	}

	return result.RequiredMargin, nil
}

// placeInitialGridOrders places all initial grid orders
func (gs *GridStrategyImpl) placeInitialGridOrders() error {
	if gs.orderManager == nil {
		return fmt.Errorf("order manager not set")
	}

	// Get current market price from the market condition
	if gs.currentCondition == nil {
		return fmt.Errorf("no market condition available for grid calculation")
	}

	currentMarketPrice := gs.currentCondition.Price
	fmt.Printf("🔄 Recalculating grid levels with current market price: %.7f\n", currentMarketPrice)

	// Recalculate grid levels with current market price
	result, err := gs.calculator.CalculateGridLevelsWithMarketPrice(currentMarketPrice)
	if err != nil {
		return fmt.Errorf("failed to recalculate grid levels with market price: %w", err)
	}

	// Update the strategy levels with the new calculation
	gs.Levels = result.Levels

	// Skip actual order placement in dry run mode, but show what would be placed
	if gs.Config.Trading.DryRun {
		fmt.Println("⚠️  Dry run mode: showing calculated grid levels without placing orders")

		buyCount := 0
		sellCount := 0
		for _, level := range gs.Levels {
			if level.Side == futures.SideTypeBuy {
				buyCount++
			} else {
				sellCount++
			}
		}

		fmt.Printf("📊 Grid Analysis:\n")
		fmt.Printf("   Current Market Price: %.7f\n", currentMarketPrice)
		fmt.Printf("   Total Grid Levels: %d\n", len(gs.Levels))
		fmt.Printf("   BUY orders: %d\n", buyCount)
		fmt.Printf("   SELL orders: %d\n", sellCount)

		fmt.Printf("\n📝 First 10 grid levels:\n")
		for i, level := range gs.Levels {
			if i >= 10 {
				break
			}
			fmt.Printf("   Level %d: %s %.0f at %.7f\n", level.ID, level.Side, level.Quantity, level.Price)
		}

		gs.sendEvent(GridEventType("dry_run_orders"), nil, 0, "Dry run mode: grid levels calculated but orders not placed")
		return nil
	}

	fmt.Printf("🚀 Starting to place %d initial grid orders...\n", len(gs.Levels))
	ordersPlaced := 0
	for _, level := range gs.Levels {
		// Skip levels that already have orders
		if level.Status != config.GridLevelStatusPending {
			continue
		}

		// Adjust price and quantity for exchange precision requirements
		var adjustedPrice, adjustedQuantity float64
		var quantityStr, priceStr string
		var err error

		if gs.precisionManager != nil {
			// Store original values for comparison
			originalPrice := level.Price
			originalQuantity := level.Quantity

			// Adjust price and quantity to meet exchange requirements
			adjustedPrice, adjustedQuantity, err = gs.precisionManager.AdjustOrderForPrecision(
				gs.Config.Trading.Symbol, level.Price, level.Quantity)
			if err != nil {
				fmt.Printf("❌ Failed to adjust precision for level %d: %v\n", level.ID, err)
				continue
			}

			// Update the level with adjusted values
			level.Price = adjustedPrice
			level.Quantity = adjustedQuantity

			// Format with proper precision
			quantityStr, err = gs.precisionManager.FormatQuantity(gs.Config.Trading.Symbol, adjustedQuantity)
			if err != nil {
				fmt.Printf("❌ Failed to format quantity for level %d: %v, using fallback\n", level.ID, err)
				quantityStr = strconv.FormatFloat(adjustedQuantity, 'f', 0, 64)
			}

			priceStr, err = gs.precisionManager.FormatPrice(gs.Config.Trading.Symbol, adjustedPrice)
			if err != nil {
				fmt.Printf("❌ Failed to format price for level %d: %v, using fallback\n", level.ID, err)
				priceStr = strconv.FormatFloat(adjustedPrice, 'f', 7, 64)
			}

			// Only show adjustment message if values actually changed
			if originalPrice != adjustedPrice || originalQuantity != adjustedQuantity {
				fmt.Printf("✅ Level %d adjusted: Price %.8f -> %.8f, Quantity %.2f -> %.0f\n",
					level.ID, originalPrice, adjustedPrice, originalQuantity, adjustedQuantity)
			}
		} else {
			// Fallback to hardcoded precision (for backward compatibility)
			fmt.Printf("⚠️  Using fallback precision formatting for level %d\n", level.ID)
			quantityStr = strconv.FormatFloat(level.Quantity, 'f', 0, 64)  // Round to whole numbers
			priceStr = strconv.FormatFloat(level.Price, 'f', 7, 64)        // 7 decimal places for price
		}

		request := &orders.PlaceOrderRequest{
			Symbol:       gs.Config.Trading.Symbol,
			Side:         level.Side,
			Type:         futures.OrderTypeLimit,
			Quantity:     quantityStr,
			Price:        priceStr,
			TimeInForce:  futures.TimeInForceTypeGTC,
			PositionSide: futures.PositionSideTypeBoth,
			ReduceOnly:   false,
			GridLevelID:  level.ID,
		}

		fmt.Printf("📝 Placing order %d: %s %.0f at %.7f\n", level.ID, level.Side, level.Quantity, level.Price)

		// Place order synchronously to see any errors
		response, err := gs.orderManager.PlaceOrder(request)
		if err != nil {
			fmt.Printf("❌ Failed to place order for level %d: %v\n", level.ID, err)
			gs.sendEvent(GridEventType("order_placement_error"), level, level.Price,
				fmt.Sprintf("Failed to place order for level %d: %v", level.ID, err))
			continue
		}

		if !response.Success {
			fmt.Printf("❌ Order placement failed for level %d: %v\n", level.ID, response.Error)
			gs.sendEvent(GridEventType("order_placement_error"), level, level.Price,
				fmt.Sprintf("Order placement failed for level %d: %v", level.ID, response.Error))
			continue
		}

		// Update level status
		level.Status = config.GridLevelStatusActive
		level.UpdatedAt = time.Now()
		ordersPlaced++

		fmt.Printf("✅ Successfully placed order for level %d (Order ID: %d)\n", level.ID, response.Order.ID)
		gs.sendEvent(GridEventType("order_placed"), level, level.Price,
			fmt.Sprintf("Placed %s order for %.4f at price %.4f", level.Side, level.Quantity, level.Price))
	}

	fmt.Printf("🎯 Queued %d initial grid orders for placement\n", ordersPlaced)
	gs.sendEvent(GridEventType("initial_orders_placed"), nil, 0,
		fmt.Sprintf("Placed %d initial grid orders", ordersPlaced))

	return nil
}

// placeInitialMarketBuyOrder places an initial market buy order for long grid strategy
func (gs *GridStrategyImpl) placeInitialMarketBuyOrder(currentPrice float64) error {
	if gs.orderManager == nil {
		return fmt.Errorf("order manager not set")
	}

	// Calculate intelligent initial position size based on grid composition
	initialQuantity, err := gs.calculateOptimalInitialPosition(currentPrice)
	if err != nil {
		return fmt.Errorf("failed to calculate optimal initial position: %w", err)
	}

	// Adjust quantity for precision requirements
	var adjustedQuantity float64
	var quantityStr string

	if gs.precisionManager != nil {
		adjustedQuantity, err = gs.precisionManager.RoundQuantity(gs.Config.Trading.Symbol, initialQuantity)
		if err != nil {
			return fmt.Errorf("failed to round initial buy quantity: %w", err)
		}

		quantityStr, err = gs.precisionManager.FormatQuantity(gs.Config.Trading.Symbol, adjustedQuantity)
		if err != nil {
			return fmt.Errorf("failed to format initial buy quantity: %w", err)
		}

		// Validate the adjusted quantity
		if err := gs.precisionManager.ValidateQuantity(gs.Config.Trading.Symbol, adjustedQuantity); err != nil {
			return fmt.Errorf("initial buy quantity validation failed: %w", err)
		}
	} else {
		// Fallback formatting
		adjustedQuantity = initialQuantity
		quantityStr = fmt.Sprintf("%.0f", adjustedQuantity)
	}

	fmt.Printf("🚀 Placing initial market BUY order: %.0f at market price %.7f\n", adjustedQuantity, currentPrice)

	// Create a placeholder grid level for the initial market buy order BEFORE placing the order
	// This prevents race conditions where WebSocket fill events arrive before the level is created
	initialLevel := &GridLevel{
		ID:        -1, // Special ID for initial market order
		Price:     currentPrice,
		Side:      futures.SideTypeBuy,
		Quantity:  adjustedQuantity,
		Status:    config.GridLevelStatusActive,
		OrderID:   -1, // Temporary placeholder, will be updated after order placement
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Add initial level to grid levels BEFORE placing the order
	gs.Levels = append(gs.Levels, initialLevel)

	// Create market buy order request
	request := &orders.PlaceOrderRequest{
		Symbol:       gs.Config.Trading.Symbol,
		Side:         futures.SideTypeBuy,
		Type:         futures.OrderTypeMarket,
		Quantity:     quantityStr,
		TimeInForce:  futures.TimeInForceTypeGTC,
		PositionSide: futures.PositionSideTypeBoth,
		ReduceOnly:   false,
		GridLevelID:  -1, // Special ID for initial market order
	}

	// Place the market order
	response, err := gs.orderManager.PlaceOrder(request)
	if err != nil {
		// Remove the placeholder level if order placement fails
		gs.Levels = gs.Levels[:len(gs.Levels)-1]
		return fmt.Errorf("failed to place initial market buy order: %w", err)
	}

	if !response.Success {
		// Remove the placeholder level if order placement fails
		gs.Levels = gs.Levels[:len(gs.Levels)-1]
		return fmt.Errorf("initial market buy order failed: %v", response.Error)
	}

	// Update the initial level with the actual order ID
	initialLevel.OrderID = response.Order.ID
	initialLevel.UpdatedAt = time.Now()

	fmt.Printf("✅ Successfully placed initial market BUY order (Order ID: %d)\n", response.Order.ID)
	gs.sendEvent(GridEventType("initial_market_buy_placed"), initialLevel, currentPrice,
		fmt.Sprintf("Placed initial market buy order for %.4f at market price", adjustedQuantity))

	return nil
}

// calculateOptimalInitialPosition calculates the optimal initial position size based on grid composition
func (gs *GridStrategyImpl) calculateOptimalInitialPosition(currentPrice float64) (float64, error) {
	if len(gs.Levels) == 0 {
		return 0, fmt.Errorf("no grid levels available")
	}

	// Analyze grid composition
	gridAnalysis := gs.analyzeGridComposition(currentPrice)

	// Calculate base position size using multiple strategies
	strategies := []struct {
		name   string
		weight float64
		calc   func(*GridCompositionAnalysis) float64
	}{
		{"proportional_to_buy_orders", 0.4, gs.calculateProportionalToBuyOrders},
		{"market_position_based", 0.3, gs.calculateMarketPositionBased},
		{"risk_adjusted", 0.2, gs.calculateRiskAdjusted},
		{"grid_type_based", 0.1, gs.calculateGridTypeBased},
	}

	var weightedQuantity float64
	fmt.Printf("📊 Initial Position Calculation Analysis:\n")
	fmt.Printf("   Grid Type: %s\n", gs.Config.Grid.Type)
	fmt.Printf("   Current Price: %.7f (%.1f%% through range)\n",
		currentPrice, gridAnalysis.MarketPositionPercent*100)
	fmt.Printf("   BUY Orders: %d (%.1f%% of total, %.2f total quantity)\n",
		gridAnalysis.BuyOrderCount, gridAnalysis.BuyOrderPercent*100, gridAnalysis.TotalBuyQuantity)
	fmt.Printf("   SELL Orders: %d (%.1f%% of total, %.2f total quantity)\n",
		gridAnalysis.SellOrderCount, gridAnalysis.SellOrderPercent*100, gridAnalysis.TotalSellQuantity)

	for _, strategy := range strategies {
		quantity := strategy.calc(gridAnalysis)
		weightedQuantity += quantity * strategy.weight
		fmt.Printf("   %s: %.2f (weight: %.1f%%)\n",
			strategy.name, quantity, strategy.weight*100)
	}

	fmt.Printf("   Final Weighted Quantity: %.2f\n", weightedQuantity)

	return weightedQuantity, nil
}

// analyzeGridComposition analyzes the composition of grid orders
func (gs *GridStrategyImpl) analyzeGridComposition(currentPrice float64) *GridCompositionAnalysis {
	analysis := &GridCompositionAnalysis{}

	leverage := float64(gs.Config.Trading.Leverage)
	priceRange := gs.Config.Grid.PriceRange.Upper - gs.Config.Grid.PriceRange.Lower

	// Calculate market position percentage (0.0 = lower bound, 1.0 = upper bound)
	analysis.MarketPositionPercent = (currentPrice - gs.Config.Grid.PriceRange.Lower) / priceRange

	var totalBuyPrice, totalSellPrice float64

	for _, level := range gs.Levels {
		analysis.TotalOrders++
		analysis.TotalQuantity += level.Quantity

		// Calculate margin for this level
		margin := (level.Quantity * level.Price) / leverage
		analysis.TotalMargin += margin

		if level.Side == futures.SideTypeBuy {
			analysis.BuyOrderCount++
			analysis.TotalBuyQuantity += level.Quantity
			analysis.TotalBuyMargin += margin
			totalBuyPrice += level.Price

			if level.Price < currentPrice {
				analysis.BuyOrdersBelowMarket++
			}
		} else {
			analysis.SellOrderCount++
			analysis.TotalSellQuantity += level.Quantity
			analysis.TotalSellMargin += margin
			totalSellPrice += level.Price

			if level.Price > currentPrice {
				analysis.SellOrdersAboveMarket++
			}
		}
	}

	// Calculate percentages
	if analysis.TotalOrders > 0 {
		analysis.BuyOrderPercent = float64(analysis.BuyOrderCount) / float64(analysis.TotalOrders)
		analysis.SellOrderPercent = float64(analysis.SellOrderCount) / float64(analysis.TotalOrders)
	}

	if analysis.TotalQuantity > 0 {
		analysis.BuyQuantityPercent = analysis.TotalBuyQuantity / analysis.TotalQuantity
		analysis.SellQuantityPercent = analysis.TotalSellQuantity / analysis.TotalQuantity
	}

	// Calculate average prices
	if analysis.BuyOrderCount > 0 {
		analysis.AvgBuyPrice = totalBuyPrice / float64(analysis.BuyOrderCount)
	}
	if analysis.SellOrderCount > 0 {
		analysis.AvgSellPrice = totalSellPrice / float64(analysis.SellOrderCount)
	}

	return analysis
}

// calculateProportionalToBuyOrders calculates position size proportional to total buy orders
func (gs *GridStrategyImpl) calculateProportionalToBuyOrders(analysis *GridCompositionAnalysis) float64 {
	// Base the initial position on a percentage of total planned buy quantity
	// This ensures the initial position is aligned with the grid's buy-side exposure
	proportionFactor := 0.25 // 25% of total buy quantity as initial position

	if analysis.TotalBuyQuantity > 0 {
		return analysis.TotalBuyQuantity * proportionFactor
	}

	// Fallback: use traditional margin-based calculation
	leverage := float64(gs.Config.Trading.Leverage)
	fallbackMargin := gs.Config.Trading.InitialMargin * 0.2
	return (fallbackMargin * leverage) / analysis.AvgBuyPrice
}

// calculateMarketPositionBased calculates position size based on market position within grid range
func (gs *GridStrategyImpl) calculateMarketPositionBased(analysis *GridCompositionAnalysis) float64 {
	// Adjust position size based on where current price sits in the grid range
	// If price is near the bottom, take a larger initial position (more upside potential)
	// If price is near the top, take a smaller initial position (less upside potential)

	leverage := float64(gs.Config.Trading.Leverage)
	baseMargin := gs.Config.Trading.InitialMargin * 0.25

	// Position multiplier based on market position (inverted - lower price = higher multiplier)
	positionMultiplier := 1.5 - analysis.MarketPositionPercent // Range: 0.5 to 1.5

	adjustedMargin := baseMargin * positionMultiplier
	currentPrice := gs.Config.Grid.PriceRange.Lower +
		(gs.Config.Grid.PriceRange.Upper-gs.Config.Grid.PriceRange.Lower)*analysis.MarketPositionPercent

	return (adjustedMargin * leverage) / currentPrice
}

// calculateRiskAdjusted calculates position size based on risk management principles
func (gs *GridStrategyImpl) calculateRiskAdjusted(analysis *GridCompositionAnalysis) float64 {
	// Risk-adjusted position sizing based on:
	// 1. Total margin available
	// 2. Number of buy orders that could be filled
	// 3. Average distance to buy orders

	leverage := float64(gs.Config.Trading.Leverage)
	totalMargin := gs.Config.Trading.InitialMargin

	// Calculate risk factor based on buy orders below market
	riskFactor := 1.0
	if analysis.BuyOrderCount > 0 {
		// Higher risk if many buy orders could be triggered
		buyOrderRisk := float64(analysis.BuyOrdersBelowMarket) / float64(analysis.BuyOrderCount)
		riskFactor = 1.0 - (buyOrderRisk * 0.3) // Reduce by up to 30% if high risk
	}

	// Conservative position sizing: 15% of total margin, adjusted by risk
	conservativeMargin := totalMargin * 0.15 * riskFactor

	currentPrice := analysis.AvgBuyPrice
	if currentPrice == 0 {
		currentPrice = (gs.Config.Grid.PriceRange.Lower + gs.Config.Grid.PriceRange.Upper) / 2
	}

	return (conservativeMargin * leverage) / currentPrice
}

// calculateGridTypeBased calculates position size based on grid strategy type
func (gs *GridStrategyImpl) calculateGridTypeBased(analysis *GridCompositionAnalysis) float64 {
	leverage := float64(gs.Config.Trading.Leverage)
	baseMargin := gs.Config.Trading.InitialMargin

	var marginRatio float64

	switch gs.Config.Grid.Type {
	case config.GridTypeLong:
		// Long strategy: More aggressive initial position
		// Scale based on buy order dominance
		marginRatio = 0.35 + (analysis.BuyQuantityPercent * 0.15) // 35-50% of margin

	case config.GridTypeShort:
		// Short strategy: Smaller initial buy (mainly for covering)
		marginRatio = 0.10 // 10% of margin

	case config.GridTypeNeutral:
		// Neutral strategy: Moderate initial position
		marginRatio = 0.20 // 20% of margin

	default:
		marginRatio = 0.25 // Default 25%
	}

	initialMargin := baseMargin * marginRatio
	currentPrice := (gs.Config.Grid.PriceRange.Lower + gs.Config.Grid.PriceRange.Upper) / 2

	return (initialMargin * leverage) / currentPrice
}

// regenerateGridOrder handles automatic order regeneration when an order is filled
func (gs *GridStrategyImpl) regenerateGridOrder(request *GridRegenerationRequest) (*GridRegenerationResult, error) {
	if gs.orderManager == nil {
		return &GridRegenerationResult{
			Success: false,
			Error:   fmt.Errorf("order manager not set"),
		}, nil
	}

	filledLevel := request.FilledLevel

	// Generate unique cycle ID if not exists
	if filledLevel.CycleID == nil {
		cycleID := fmt.Sprintf("cycle_%d_%d", filledLevel.ID, time.Now().UnixNano())
		filledLevel.CycleID = &cycleID
	}

	// Check if this completes a cycle (has a paired order)
	if filledLevel.PairOrderID != nil {
		// This completes a cycle - calculate profit
		cycle, err := gs.calculateCompletedCycle(filledLevel, request.FillPrice, request.FillQuantity)
		if err != nil {
			return &GridRegenerationResult{
				Success:        false,
				CycleCompleted: false,
				Error:          fmt.Errorf("failed to calculate completed cycle: %w", err),
			}, nil
		}

		// Store completed cycle
		gs.completedCycles[*filledLevel.CycleID] = cycle

		// Update statistics
		gs.updateCycleStatistics(cycle)

		fmt.Printf("🎯 Grid Cycle Completed! Profit: %.4f USDT (%.2f%%) in %v\n",
			cycle.NetProfit, cycle.ProfitPercent, cycle.Duration)

		return &GridRegenerationResult{
			Success:        true,
			CycleCompleted: true,
			CompletedCycle: cycle,
		}, nil
	}

	// Generate opposite order
	regeneratedLevel, err := gs.calculateRegeneratedOrder(filledLevel, request)
	if err != nil {
		return &GridRegenerationResult{
			Success: false,
			Error:   fmt.Errorf("failed to calculate regenerated order: %w", err),
		}, nil
	}

	// Place the regenerated order
	orderID, err := gs.placeRegeneratedOrder(regeneratedLevel)
	if err != nil {
		return &GridRegenerationResult{
			Success: false,
			Error:   fmt.Errorf("failed to place regenerated order: %w", err),
		}, nil
	}

	// Update relationships
	regeneratedLevel.OrderID = orderID
	filledLevel.RegeneratedOrderID = &orderID
	regeneratedLevel.PairOrderID = &filledLevel.OrderID

	// Add to pending cycles for future completion
	gs.pendingCycles[orderID] = filledLevel

	// Add regenerated level to grid levels
	gs.Levels = append(gs.Levels, regeneratedLevel)

	// Update statistics
	gs.Statistics.RegeneratedOrders++

	fmt.Printf("🔄 Regenerated %s order: %.0f at %.7f (paired with order %d)\n",
		regeneratedLevel.Side, regeneratedLevel.Quantity, regeneratedLevel.Price, filledLevel.OrderID)

	return &GridRegenerationResult{
		Success:          true,
		RegeneratedLevel: regeneratedLevel,
		OrderID:          orderID,
		CycleCompleted:   false,
	}, nil
}

// calculateRegeneratedOrder calculates the opposite order for a filled order
func (gs *GridStrategyImpl) calculateRegeneratedOrder(filledLevel *GridLevel, request *GridRegenerationRequest) (*GridLevel, error) {
	// Determine opposite side
	var oppositeSide futures.SideType
	if filledLevel.Side == futures.SideTypeBuy {
		oppositeSide = futures.SideTypeSell
	} else {
		oppositeSide = futures.SideTypeBuy
	}

	// Calculate regenerated price based on grid spacing
	var regeneratedPrice float64
	if oppositeSide == futures.SideTypeSell {
		// For sell orders, place above the buy price
		regeneratedPrice = request.FillPrice + request.GridSpacing
	} else {
		// For buy orders, place below the sell price
		regeneratedPrice = request.FillPrice - request.GridSpacing
	}

	// Ensure price is within grid range
	if regeneratedPrice < gs.Config.Grid.PriceRange.Lower || regeneratedPrice > gs.Config.Grid.PriceRange.Upper {
		return nil, fmt.Errorf("regenerated price %.7f is outside grid range [%.7f, %.7f]",
			regeneratedPrice, gs.Config.Grid.PriceRange.Lower, gs.Config.Grid.PriceRange.Upper)
	}

	// Use same quantity as filled order
	regeneratedQuantity := request.FillQuantity

	// Create new grid level
	regeneratedLevel := &GridLevel{
		ID:                 len(gs.Levels) + 1000, // Use high ID to avoid conflicts
		Price:              regeneratedPrice,
		Side:               oppositeSide,
		Quantity:           regeneratedQuantity,
		Status:             config.GridLevelStatusPending,
		CreatedAt:          time.Now(),
		UpdatedAt:          time.Now(),
		IsRegenerated:      true,
		OriginalLevelID:    &filledLevel.ID,
		CycleID:            filledLevel.CycleID,
		FillPrice:          &request.FillPrice,
		FillQuantity:       &request.FillQuantity,
	}

	return regeneratedLevel, nil
}

// placeRegeneratedOrder places a regenerated order
func (gs *GridStrategyImpl) placeRegeneratedOrder(level *GridLevel) (int64, error) {
	// Adjust price and quantity for precision requirements
	var adjustedPrice, adjustedQuantity float64
	var quantityStr, priceStr string
	var err error

	if gs.precisionManager != nil {
		adjustedPrice, adjustedQuantity, err = gs.precisionManager.AdjustOrderForPrecision(
			gs.Config.Trading.Symbol, level.Price, level.Quantity)
		if err != nil {
			return 0, fmt.Errorf("failed to adjust precision: %w", err)
		}

		quantityStr, err = gs.precisionManager.FormatQuantity(gs.Config.Trading.Symbol, adjustedQuantity)
		if err != nil {
			return 0, fmt.Errorf("failed to format quantity: %w", err)
		}
		priceStr, err = gs.precisionManager.FormatPrice(gs.Config.Trading.Symbol, adjustedPrice)
		if err != nil {
			return 0, fmt.Errorf("failed to format price: %w", err)
		}
	} else {
		adjustedPrice = level.Price
		adjustedQuantity = level.Quantity
		quantityStr = fmt.Sprintf("%.8f", adjustedQuantity)
		priceStr = fmt.Sprintf("%.8f", adjustedPrice)
	}

	// Update level with adjusted values
	level.Price = adjustedPrice
	level.Quantity = adjustedQuantity

	// Create order request
	request := &orders.PlaceOrderRequest{
		Symbol:       gs.Config.Trading.Symbol,
		Side:         level.Side,
		Type:         futures.OrderTypeLimit,
		Quantity:     quantityStr,
		Price:        priceStr,
		TimeInForce:  futures.TimeInForceTypeGTC,
		PositionSide: futures.PositionSideTypeBoth,
		ReduceOnly:   false,
		GridLevelID:  level.ID,
	}

	// Place order
	response, err := gs.orderManager.PlaceOrder(request)
	if err != nil {
		return 0, fmt.Errorf("failed to place regenerated order: %w", err)
	}

	if !response.Success {
		return 0, fmt.Errorf("regenerated order placement failed: %v", response.Error)
	}

	// Update level status
	level.Status = config.GridLevelStatusActive
	level.UpdatedAt = time.Now()

	return response.Order.ID, nil
}

// calculateCompletedCycle calculates profit for a completed buy-sell cycle
func (gs *GridStrategyImpl) calculateCompletedCycle(filledLevel *GridLevel, fillPrice, fillQuantity float64) (*GridCycle, error) {
	// Find the paired order
	pairedLevel, exists := gs.pendingCycles[*filledLevel.PairOrderID]
	if !exists {
		return nil, fmt.Errorf("paired order %d not found in pending cycles", *filledLevel.PairOrderID)
	}

	// Determine buy and sell prices
	var buyPrice, sellPrice, buyQuantity, sellQuantity float64
	var buyOrderID, sellOrderID int64
	var cycleDuration time.Duration

	if filledLevel.Side == futures.SideTypeBuy {
		// Current order is buy, paired order is sell
		buyPrice = fillPrice
		buyQuantity = fillQuantity
		buyOrderID = filledLevel.OrderID

		sellPrice = *pairedLevel.FillPrice
		sellQuantity = *pairedLevel.FillQuantity
		sellOrderID = pairedLevel.OrderID

		cycleDuration = time.Since(*pairedLevel.FilledAt)
	} else {
		// Current order is sell, paired order is buy
		sellPrice = fillPrice
		sellQuantity = fillQuantity
		sellOrderID = filledLevel.OrderID

		buyPrice = *pairedLevel.FillPrice
		buyQuantity = *pairedLevel.FillQuantity
		buyOrderID = pairedLevel.OrderID

		cycleDuration = time.Since(*pairedLevel.FilledAt)
	}

	// Use the smaller quantity for profit calculation
	quantity := buyQuantity
	if sellQuantity < buyQuantity {
		quantity = sellQuantity
	}

	// Calculate profit
	profit := (sellPrice - buyPrice) * quantity
	profitPercent := (profit / (buyPrice * quantity)) * 100

	// Estimate commission (typically 0.02% for futures)
	commissionRate := 0.0002 // 0.02%
	commission := (buyPrice*quantity + sellPrice*quantity) * commissionRate
	netProfit := profit - commission

	cycle := &GridCycle{
		ID:            *filledLevel.CycleID,
		BuyOrderID:    buyOrderID,
		SellOrderID:   sellOrderID,
		BuyPrice:      buyPrice,
		SellPrice:     sellPrice,
		Quantity:      quantity,
		Profit:        profit,
		ProfitPercent: profitPercent,
		Commission:    commission,
		NetProfit:     netProfit,
		CreatedAt:     pairedLevel.CreatedAt,
		CompletedAt:   time.Now(),
		Duration:      cycleDuration,
	}

	// Remove from pending cycles
	delete(gs.pendingCycles, *filledLevel.PairOrderID)

	return cycle, nil
}

// updateCycleStatistics updates strategy statistics with completed cycle data
func (gs *GridStrategyImpl) updateCycleStatistics(cycle *GridCycle) {
	gs.Statistics.CompletedCycles++
	gs.Statistics.TotalCycleProfit += cycle.NetProfit
	gs.Statistics.NetPnL += cycle.NetProfit
	gs.Statistics.TotalPnL += cycle.Profit

	// Update average cycle profit
	if gs.Statistics.CompletedCycles > 0 {
		gs.Statistics.AvgCycleProfit = gs.Statistics.TotalCycleProfit / float64(gs.Statistics.CompletedCycles)
	}

	// Update average cycle duration
	totalDuration := time.Duration(0)
	for _, completedCycle := range gs.completedCycles {
		totalDuration += completedCycle.Duration
	}
	if gs.Statistics.CompletedCycles > 0 {
		gs.Statistics.AvgCycleDuration = totalDuration / time.Duration(gs.Statistics.CompletedCycles)
	}

	// Update best/worst cycle profits
	if gs.Statistics.BestCycleProfit == 0 || cycle.NetProfit > gs.Statistics.BestCycleProfit {
		gs.Statistics.BestCycleProfit = cycle.NetProfit
	}
	if gs.Statistics.WorstCycleProfit == 0 || cycle.NetProfit < gs.Statistics.WorstCycleProfit {
		gs.Statistics.WorstCycleProfit = cycle.NetProfit
	}

	// Update profitable trades count
	if cycle.NetProfit > 0 {
		gs.Statistics.ProfitableTrades++
	}

	// Update last trade time
	now := time.Now()
	gs.Statistics.LastTradeTime = &now
}

// GetCompletedCycles returns all completed grid cycles
func (gs *GridStrategyImpl) GetCompletedCycles() map[string]*GridCycle {
	gs.mu.RLock()
	defer gs.mu.RUnlock()

	// Return a copy to prevent external modification
	cycles := make(map[string]*GridCycle)
	for id, cycle := range gs.completedCycles {
		cycleCopy := *cycle
		cycles[id] = &cycleCopy
	}
	return cycles
}

// GetPendingCycles returns all pending cycles (orders waiting for pairs)
func (gs *GridStrategyImpl) GetPendingCycles() map[int64]*GridLevel {
	gs.mu.RLock()
	defer gs.mu.RUnlock()

	// Return a copy to prevent external modification
	pending := make(map[int64]*GridLevel)
	for orderID, level := range gs.pendingCycles {
		levelCopy := *level
		pending[orderID] = &levelCopy
	}
	return pending
}

// GetCycleStatistics returns detailed cycle statistics
func (gs *GridStrategyImpl) GetCycleStatistics() map[string]interface{} {
	gs.mu.RLock()
	defer gs.mu.RUnlock()

	stats := make(map[string]interface{})
	stats["completed_cycles"] = gs.Statistics.CompletedCycles
	stats["total_cycle_profit"] = gs.Statistics.TotalCycleProfit
	stats["avg_cycle_profit"] = gs.Statistics.AvgCycleProfit
	stats["avg_cycle_duration"] = gs.Statistics.AvgCycleDuration.String()
	stats["best_cycle_profit"] = gs.Statistics.BestCycleProfit
	stats["worst_cycle_profit"] = gs.Statistics.WorstCycleProfit
	stats["regenerated_orders"] = gs.Statistics.RegeneratedOrders
	stats["pending_cycles"] = len(gs.pendingCycles)

	return stats
}
