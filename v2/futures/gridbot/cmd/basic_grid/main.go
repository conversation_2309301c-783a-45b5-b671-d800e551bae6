package main

import (
	"fmt"
	"log"
	"os"
	"os/signal"
	"strconv"
	"syscall"
	"time"

	"github.com/adshao/go-binance/v2/futures"
	"github.com/adshao/go-binance/v2/futures/gridbot/config"
	"github.com/adshao/go-binance/v2/futures/gridbot/market"
	"github.com/adshao/go-binance/v2/futures/gridbot/orders"
	"github.com/adshao/go-binance/v2/futures/gridbot/precision"
	"github.com/adshao/go-binance/v2/futures/gridbot/strategy"
)

func main() {
	// Determine configuration file
	configFile := "config/examples/basic_grid.yaml" // default
	if len(os.Args) > 1 {
		configFile = os.Args[1]
	}

	// Load configuration
	cfg, err := config.LoadConfig(configFile)
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Override with environment variables if available
	if apiKey := os.Getenv("BINANCE_API_KEY"); apiKey != "" {
		cfg.API.APIKey = apiKey
	}
	if secretKey := os.Getenv("BINANCE_SECRET_KEY"); secretKey != "" {
		cfg.API.SecretKey = secretKey
	}

	// Set testnet if specified
	if cfg.API.Testnet {
		futures.UseTestnet = true
	}

	// Create Binance futures client
	client := futures.NewClient(cfg.API.APIKey, cfg.API.SecretKey)

	// Create components
	marketHandler := market.NewMarketDataHandler(client, cfg, cfg.Trading.Symbol)
	orderManager := orders.NewOrderManager(client, cfg)
	precisionManager := precision.NewPrecisionManager(client, cfg)
	gridStrategy := strategy.NewGridStrategy(cfg)

	// Set dependencies on strategy
	gridStrategy.SetOrderManager(orderManager)
	gridStrategy.SetPrecisionManager(precisionManager)

	// Print symbol precision info for debugging
	fmt.Printf("📊 Fetching precision information for %s...\n", cfg.Trading.Symbol)
	if err := precisionManager.PrintSymbolInfo(cfg.Trading.Symbol); err != nil {
		fmt.Printf("⚠️  Warning: Could not fetch precision info: %v\n", err)
		fmt.Printf("⚠️  Will use fallback precision values\n")
	}

	// Create the main grid bot
	bot := &GridBot{
		config:           cfg,
		client:           client,
		marketHandler:    marketHandler,
		orderManager:     orderManager,
		precisionManager: precisionManager,
		strategy:         gridStrategy,
		stopChan:         make(chan struct{}),
	}

	// Set up signal handling for graceful shutdown
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// Start the bot
	if err := bot.Start(); err != nil {
		log.Fatalf("Failed to start grid bot: %v", err)
	}

	fmt.Println("Grid trading bot started successfully!")
	fmt.Printf("Trading %s with %dx leverage\n", cfg.Trading.Symbol, cfg.Trading.Leverage)
	// Use dynamic precision for display
	displayPrecision, err := precisionManager.GetDisplayPrecision(cfg.Trading.Symbol)
	if err != nil {
		displayPrecision = 7 // fallback
	}
	fmt.Printf("Grid range: %.*f - %.*f with %d levels\n",
		displayPrecision, cfg.Grid.PriceRange.Lower, displayPrecision, cfg.Grid.PriceRange.Upper, cfg.Grid.GridCount)
	fmt.Printf("Initial margin: %.2f USDT\n", cfg.Trading.InitialMargin)
	
	if cfg.Trading.DryRun {
		fmt.Println("⚠️  Running in DRY RUN mode - no real trades will be executed")
	}

	// Run the bot
	go bot.Run()

	// Start order event listener
	go bot.listenToOrderEvents()

	// Start user data updates listener
	go bot.listenToUserDataUpdates()

	// Wait for shutdown signal
	<-sigChan
	fmt.Println("\nShutdown signal received, stopping grid bot...")

	// Stop the bot gracefully
	if err := bot.Stop(); err != nil {
		log.Printf("Error stopping bot: %v", err)
	}

	fmt.Println("Grid trading bot stopped successfully!")
}

// GridBot represents the main grid trading bot
type GridBot struct {
	config           *config.Config
	client           *futures.Client
	marketHandler    market.MarketDataHandlerInterface
	orderManager     orders.OrderManagerInterface
	precisionManager *precision.PrecisionManager
	strategy         strategy.StrategyInterface

	running       bool
	stopChan      chan struct{}
}

// Start starts the grid bot
func (bot *GridBot) Start() error {
	// Start market data handler
	if err := bot.marketHandler.Start(); err != nil {
		return fmt.Errorf("failed to start market handler: %w", err)
	}

	// Subscribe to market data
	if err := bot.marketHandler.SubscribePriceUpdates(); err != nil {
		return fmt.Errorf("failed to subscribe to price updates: %w", err)
	}

	if err := bot.marketHandler.SubscribeDepthUpdates(); err != nil {
		return fmt.Errorf("failed to subscribe to depth updates: %w", err)
	}

	// Skip user data subscription in dry run mode to avoid network dependency
	if !bot.config.Trading.DryRun {
		if err := bot.marketHandler.SubscribeUserDataUpdates(); err != nil {
			return fmt.Errorf("failed to subscribe to user data updates: %w", err)
		}
	}

	// Start order manager
	if err := bot.orderManager.Start(); err != nil {
		return fmt.Errorf("failed to start order manager: %w", err)
	}

	// Initialize and start strategy
	if err := bot.strategy.Initialize(); err != nil {
		return fmt.Errorf("failed to initialize strategy: %w", err)
	}

	if err := bot.strategy.Start(); err != nil {
		return fmt.Errorf("failed to start strategy: %w", err)
	}

	bot.running = true
	return nil
}

// Stop stops the grid bot
func (bot *GridBot) Stop() error {
	if !bot.running {
		return nil
	}

	bot.running = false

	// Signal stop
	close(bot.stopChan)

	// Stop strategy
	if err := bot.strategy.Stop(); err != nil {
		log.Printf("Error stopping strategy: %v", err)
	}

	// Cancel all active orders
	if err := bot.orderManager.CancelAllOrders(bot.config.Trading.Symbol); err != nil {
		log.Printf("Error cancelling orders: %v", err)
	}

	// Stop order manager
	if err := bot.orderManager.Stop(); err != nil {
		log.Printf("Error stopping order manager: %v", err)
	}

	// Stop market handler
	if err := bot.marketHandler.Stop(); err != nil {
		log.Printf("Error stopping market handler: %v", err)
	}

	return nil
}

// listenToOrderEvents listens to order events and forwards them to the strategy
func (bot *GridBot) listenToOrderEvents() {
	orderEvents := bot.orderManager.GetOrderEvents()
	fmt.Printf("🎧 Order event listener started\n")

	for {
		select {
		case event := <-orderEvents:
			if event == nil {
				continue
			}

			fmt.Printf("📨 Received order event: Type=%s, OrderID=%d\n", event.Type,
				func() int64 { if event.Order != nil { return event.Order.ID } else { return -1 } }())

			// Handle order fill events
			if event.Type == "order_filled" && event.Order != nil {
				// Extract fill information from the order
				// Use AvgPrice for fill price and ExecutedQuantity for fill quantity
				fillPriceStr := event.Order.AvgPrice
				fillQuantityStr := event.Order.ExecutedQuantity

				// Convert strings to float64
				fillPrice, err := strconv.ParseFloat(fillPriceStr, 64)
				if err != nil {
					log.Printf("❌ Error parsing fill price '%s' for order %d: %v", fillPriceStr, event.Order.ID, err)
					continue
				}

				fillQuantity, err := strconv.ParseFloat(fillQuantityStr, 64)
				if err != nil {
					log.Printf("❌ Error parsing fill quantity '%s' for order %d: %v", fillQuantityStr, event.Order.ID, err)
					continue
				}

				// Call strategy's HandleOrderFill method
				if err := bot.strategy.HandleOrderFill(event.Order.ID, fillPrice, fillQuantity); err != nil {
					log.Printf("❌ Error handling order fill for order %d: %v", event.Order.ID, err)
				}
			}

		case <-bot.stopChan:
			return
		}
	}
}

// listenToUserDataUpdates listens to user data updates from WebSocket and processes order fills
func (bot *GridBot) listenToUserDataUpdates() {
	userDataUpdates := bot.marketHandler.GetUserDataUpdates()
	fmt.Printf("🎧 User data updates listener started\n")

	for {
		select {
		case update := <-userDataUpdates:
			if update == nil {
				continue
			}

			// Process order updates
			if update.Type == market.UserDataTypeOrderUpdate {
				if orderData, ok := update.Data.(*market.OrderUpdateData); ok {
					fmt.Printf("📨 Received WebSocket order update: OrderID=%d, Status=%s, ExecutedQty=%s\n",
						orderData.OrderID, orderData.Status, orderData.ExecutedQuantity)

					// Update order manager with the latest order information
					if err := bot.orderManager.UpdateOrderFromWebSocket(orderData); err != nil {
						log.Printf("❌ Error updating order from WebSocket: %v", err)
					}
				}
			}

		case <-bot.stopChan:
			return
		}
	}
}

// Run runs the main bot loop
func (bot *GridBot) Run() {
	ticker := time.NewTicker(bot.config.Monitoring.UpdateInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			bot.update()
		case <-bot.stopChan:
			return
		}
	}
}

// update performs periodic updates
func (bot *GridBot) update() {
	// Get current market condition
	condition := bot.marketHandler.GetMarketCondition()
	if condition == nil {
		return
	}

	// Update strategy with market condition
	if err := bot.strategy.Update(condition); err != nil {
		log.Printf("Strategy update error: %v", err)
		return
	}

	// Print status periodically
	bot.printStatus(condition)
}

// printStatus prints the current bot status
func (bot *GridBot) printStatus(condition *market.MarketCondition) {
	stats := bot.strategy.GetStatistics()
	orderStats := bot.orderManager.GetStatistics()

	// Get display precision
	displayPrecision, err := bot.precisionManager.GetDisplayPrecision(bot.config.Trading.Symbol)
	if err != nil {
		displayPrecision = 7 // fallback
	}

	fmt.Printf("\n=== Grid Bot Status ===\n")
	fmt.Printf("Time: %s\n", time.Now().Format("2006-01-02 15:04:05"))
	fmt.Printf("Symbol: %s\n", bot.config.Trading.Symbol)
	fmt.Printf("Current Price: %.*f\n", displayPrecision, condition.Price)
	fmt.Printf("Mark Price: %.*f\n", displayPrecision, condition.MarkPrice)
	fmt.Printf("24h Change: %.2f%%\n", condition.PriceChange24h)
	fmt.Printf("Volatility: %.2f%%\n", condition.Volatility)
	fmt.Printf("Trend: %s\n", condition.Trend)

	if condition.BidPrice > 0 && condition.AskPrice > 0 {
		fmt.Printf("Bid/Ask: %.*f / %.*f (Spread: %.4f%%)\n",
			displayPrecision, condition.BidPrice, displayPrecision, condition.AskPrice, condition.SpreadPercent)
	}

	fmt.Printf("\n--- Strategy Statistics ---\n")
	fmt.Printf("Status: %s\n", bot.strategy.GetStatus())
	fmt.Printf("Runtime: %s\n", stats.RunDuration)
	fmt.Printf("Total Trades: %d\n", stats.TotalTrades)
	fmt.Printf("Win Rate: %.2f%%\n", stats.WinRate)
	fmt.Printf("Total PnL: %.4f USDT\n", stats.TotalPnL)
	fmt.Printf("Net PnL: %.4f USDT\n", stats.NetPnL)
	fmt.Printf("ROI: %.2f%%\n", stats.ROI)
	fmt.Printf("Max Drawdown: %.2f%%\n", stats.MaxDrawdown)
	fmt.Printf("Grid Efficiency: %.2f%%\n", stats.GridEfficiency)

	fmt.Printf("\n--- Order Statistics ---\n")
	fmt.Printf("Active Orders: %d\n", orderStats.ActiveOrders)
	fmt.Printf("Total Orders: %d\n", orderStats.TotalOrders)
	fmt.Printf("Filled Orders: %d\n", orderStats.FilledOrders)
	fmt.Printf("Success Rate: %.2f%%\n", orderStats.SuccessRate)
	fmt.Printf("Total Volume: %.4f\n", orderStats.TotalVolume)

	// Show grid levels status
	levels := bot.strategy.GetGridLevels()
	activeLevels := 0
	filledLevels := 0
	for _, level := range levels {
		switch level.Status {
		case config.GridLevelStatusActive:
			activeLevels++
		case config.GridLevelStatusFilled:
			filledLevels++
		}
	}
	fmt.Printf("Grid Levels: %d total, %d active, %d filled\n",
		len(levels), activeLevels, filledLevels)

	// Show grid cycle statistics
	cycleStats := bot.strategy.GetCycleStatistics()
	fmt.Printf("\n--- Grid Cycle Statistics ---\n")
	fmt.Printf("Completed Cycles: %v\n", cycleStats["completed_cycles"])
	fmt.Printf("Total Cycle Profit: %.4f USDT\n", cycleStats["total_cycle_profit"])
	fmt.Printf("Average Cycle Profit: %.4f USDT\n", cycleStats["avg_cycle_profit"])
	fmt.Printf("Average Cycle Duration: %v\n", cycleStats["avg_cycle_duration"])
	fmt.Printf("Best Cycle Profit: %.4f USDT\n", cycleStats["best_cycle_profit"])
	fmt.Printf("Worst Cycle Profit: %.4f USDT\n", cycleStats["worst_cycle_profit"])
	fmt.Printf("Regenerated Orders: %v\n", cycleStats["regenerated_orders"])
	fmt.Printf("Pending Cycles: %v\n", cycleStats["pending_cycles"])

	fmt.Printf("========================\n")
}
