package main

import (
	"fmt"
	"log"
	"time"

	"github.com/adshao/go-binance/v2/futures"
	"github.com/adshao/go-binance/v2/futures/gridbot/config"
	"github.com/adshao/go-binance/v2/futures/gridbot/market"
	"github.com/adshao/go-binance/v2/futures/gridbot/orders"
	"github.com/adshao/go-binance/v2/futures/gridbot/strategy"
)

func main() {
	fmt.Printf("📉 SHORT GRID STRATEGY INTEGRATION TEST\n")
	fmt.Printf("=======================================\n")

	// Load short grid configuration
	cfg, err := config.LoadConfig("config/examples/short_grid_test.yaml")
	if err != nil {
		log.Fatalf("❌ Failed to load short grid config: %v", err)
	}

	fmt.Printf("✅ Configuration loaded: %s strategy\n", cfg.Grid.Type)
	fmt.Printf("📊 Symbol: %s | Leverage: %dx | Margin: %.0f USDT\n", 
		cfg.Trading.Symbol, cfg.Trading.Leverage, cfg.Trading.InitialMargin)
	fmt.Printf("📈 Grid Range: %.0f - %.0f | Count: %d | Type: %s\n", 
		cfg.Grid.PriceRange.Lower, cfg.Grid.PriceRange.Upper, cfg.Grid.GridCount, cfg.Grid.Calculation)

	// Set testnet
	if cfg.API.Testnet {
		futures.UseTestnet = true
		fmt.Printf("🔧 Using Binance Testnet\n")
	}

	// Create client and components
	client := futures.NewClient(cfg.API.APIKey, cfg.API.SecretKey)
	marketHandler := market.NewMarketDataHandler(client, cfg, cfg.Trading.Symbol)
	orderManager := orders.NewOrderManager(client, cfg)
	gridStrategy := strategy.NewGridStrategy(cfg)

	// Test 1: Short Grid Level Analysis
	fmt.Printf("\n=== TEST 1: SHORT GRID LEVEL ANALYSIS ===\n")
	
	calculator := strategy.NewGridCalculator(cfg)
	result, err := calculator.CalculateGridLevels()
	if err != nil {
		log.Fatalf("❌ Grid calculation failed: %v", err)
	}

	// For short strategy, we expect more sell orders in upper 70% of range
	priceRange := cfg.Grid.PriceRange.Upper - cfg.Grid.PriceRange.Lower
	sellThreshold := cfg.Grid.PriceRange.Lower + (priceRange * 0.3) // 30% threshold (sell above this)
	
	sellOrdersUpper := 0 // Sell orders in upper 70%
	buyOrdersLower := 0  // Buy orders in lower 30%
	totalBuyQty := 0.0
	totalSellQty := 0.0
	sellQtyUpper := 0.0
	buyQtyLower := 0.0

	fmt.Printf("📊 Short Strategy Analysis (Sell threshold: %.2f):\n", sellThreshold)
	fmt.Printf("📋 Grid Levels Analysis:\n")

	for i, level := range result.Levels {
		side := "BUY"
		region := "LOWER"
		
		if level.Side == futures.SideTypeSell {
			side = "SELL"
			totalSellQty += level.Quantity
			if level.Price > sellThreshold {
				sellOrdersUpper++
				sellQtyUpper += level.Quantity
				region = "UPPER"
			}
		} else {
			totalBuyQty += level.Quantity
			if level.Price <= sellThreshold {
				buyOrdersLower++
				buyQtyLower += level.Quantity
			} else {
				region = "UPPER"
			}
		}
		
		fmt.Printf("  Level %2d: %s  | Price: %8.2f | Qty: %.6f | %s region\n", 
			i+1, side, level.Price, level.Quantity, region)
	}

	// Validate short strategy characteristics
	fmt.Printf("\n📉 SHORT STRATEGY VALIDATION:\n")
	fmt.Printf("  Sell Orders (Upper 70%%): %d\n", sellOrdersUpper)
	fmt.Printf("  Buy Orders (Lower 30%%): %d\n", buyOrdersLower)
	fmt.Printf("  Total Sell Quantity: %.6f | Total Buy Quantity: %.6f\n", totalSellQty, totalBuyQty)
	fmt.Printf("  Sell Qty (Upper): %.6f | Buy Qty (Lower): %.6f\n", sellQtyUpper, buyQtyLower)
	
	// Check short strategy characteristics
	sellHeavy := totalSellQty > totalBuyQty
	concentratedSelling := sellOrdersUpper > buyOrdersLower
	
	fmt.Printf("  Sell-Heavy Distribution: %v\n", sellHeavy)
	fmt.Printf("  Concentrated Upper Selling: %v\n", concentratedSelling)
	
	if sellHeavy {
		fmt.Printf("  ✅ Strategy is sell-heavy (suitable for downtrends)\n")
	} else {
		fmt.Printf("  ⚠️  Strategy may not be optimally sell-heavy\n")
	}
	
	if concentratedSelling {
		fmt.Printf("  ✅ More sell orders in upper price region\n")
	} else {
		fmt.Printf("  ⚠️  Sell orders may not be concentrated in upper region\n")
	}

	// Test 2: Component Integration
	fmt.Printf("\n=== TEST 2: COMPONENT INTEGRATION ===\n")
	
	// Start market handler
	err = marketHandler.Start()
	if err != nil {
		log.Fatalf("❌ Failed to start market handler: %v", err)
	}
	fmt.Printf("✅ Market data handler started\n")

	// Subscribe to data feeds
	err = marketHandler.SubscribePriceUpdates()
	if err != nil {
		log.Fatalf("❌ Failed to subscribe to price updates: %v", err)
	}
	
	err = marketHandler.SubscribeDepthUpdates()
	if err != nil {
		log.Fatalf("❌ Failed to subscribe to depth updates: %v", err)
	}
	
	fmt.Printf("✅ WebSocket subscriptions active\n")

	// Start order manager
	err = orderManager.Start()
	if err != nil {
		log.Fatalf("❌ Failed to start order manager: %v", err)
	}
	fmt.Printf("✅ Order manager started\n")

	// Initialize and start strategy
	err = gridStrategy.Initialize()
	if err != nil {
		log.Fatalf("❌ Failed to initialize strategy: %v", err)
	}
	
	err = gridStrategy.Start()
	if err != nil {
		log.Fatalf("❌ Failed to start strategy: %v", err)
	}
	fmt.Printf("✅ Short grid strategy initialized and started\n")

	// Test 3: Market Volatility Analysis
	fmt.Printf("\n=== TEST 3: MARKET VOLATILITY ANALYSIS ===\n")
	fmt.Printf("⏱️  Running for 15 seconds to analyze market volatility...\n")

	updateCount := 0
	priceUpdates := 0
	depthUpdates := 0
	highVolatilityCount := 0
	lowVolatilityCount := 0
	maxVolatility := 0.0
	minVolatility := 100.0
	
	timeout := time.After(15 * time.Second)
	ticker := time.NewTicker(2 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-timeout:
			goto testComplete
			
		case <-ticker.C:
			// Process market data
			condition := marketHandler.GetMarketCondition()
			if condition != nil {
				err = gridStrategy.Update(condition)
				if err != nil {
					fmt.Printf("⚠️  Strategy update error: %v\n", err)
				} else {
					updateCount++
					
					// Analyze volatility for short strategy
					volatility := condition.Volatility
					if volatility > maxVolatility {
						maxVolatility = volatility
					}
					if volatility < minVolatility {
						minVolatility = volatility
					}
					
					if volatility > 2.0 { // High volatility threshold
						highVolatilityCount++
					} else {
						lowVolatilityCount++
					}
					
					fmt.Printf("📊 Update #%d - Price: %.2f | Trend: %s | Vol: %.2f%%\n", 
						updateCount, condition.Price, condition.Trend, volatility)
				}
			}
			
			// Count data updates
			select {
			case <-marketHandler.GetPriceUpdates():
				priceUpdates++
			default:
			}
			
			select {
			case <-marketHandler.GetDepthUpdates():
				depthUpdates++
			default:
			}
		}
	}

testComplete:
	// Test 4: Short Strategy Risk Analysis
	fmt.Printf("\n=== TEST 4: SHORT STRATEGY RISK ANALYSIS ===\n")
	
	stats := gridStrategy.GetStatistics()
	fmt.Printf("📈 Strategy Statistics:\n")
	fmt.Printf("  Status: %s\n", gridStrategy.GetStatus())
	fmt.Printf("  Runtime: %s\n", stats.RunDuration)
	fmt.Printf("  Active Grids: %d\n", stats.ActiveGrids)
	fmt.Printf("  Current Price: %.2f\n", stats.CurrentPrice)
	
	// Analyze volatility for short strategy effectiveness
	fmt.Printf("📊 Volatility Analysis:\n")
	fmt.Printf("  Max Volatility: %.2f%%\n", maxVolatility)
	fmt.Printf("  Min Volatility: %.2f%%\n", minVolatility)
	fmt.Printf("  High Volatility Periods: %d\n", highVolatilityCount)
	fmt.Printf("  Low Volatility Periods: %d\n", lowVolatilityCount)
	
	if highVolatilityCount > 0 {
		fmt.Printf("  ✅ Market showing volatility - good for short strategy profits\n")
	} else {
		fmt.Printf("  ⚠️  Low volatility may limit short strategy effectiveness\n")
	}
	
	// Check if price is in favorable region for short strategy
	currentPrice := stats.CurrentPrice
	if currentPrice > 0 {
		if currentPrice > sellThreshold {
			fmt.Printf("  ✅ Current price in upper region - good for short selling\n")
		} else {
			fmt.Printf("  📉 Current price in lower region - potential covering zone\n")
		}
	}

	// Test 5: Short Strategy Risk Management
	fmt.Printf("\n=== TEST 5: SHORT STRATEGY RISK MANAGEMENT ===\n")
	
	orderStats := orderManager.GetStatistics()
	fmt.Printf("📋 Order Manager Statistics:\n")
	fmt.Printf("  Total Orders: %d\n", orderStats.TotalOrders)
	fmt.Printf("  Active Orders: %d\n", orderStats.ActiveOrders)
	fmt.Printf("  Success Rate: %.2f%%\n", orderStats.SuccessRate)
	

	
	// Check margin requirements for short strategy
	requiredMargin, err := gridStrategy.CalculateRequiredMargin()
	if err == nil {
		marginUtilization := (requiredMargin / cfg.Trading.InitialMargin) * 100
		fmt.Printf("  Margin Utilization: %.1f%%\n", marginUtilization)
		
		if marginUtilization < 80 {
			fmt.Printf("  ✅ Margin utilization is conservative\n")
		} else {
			fmt.Printf("  ⚠️  High margin utilization - monitor closely\n")
		}
	}

	// Cleanup
	fmt.Printf("\n=== CLEANUP ===\n")
	
	err = gridStrategy.Stop()
	if err != nil {
		fmt.Printf("⚠️  Error stopping strategy: %v\n", err)
	} else {
		fmt.Printf("✅ Strategy stopped\n")
	}
	
	err = orderManager.Stop()
	if err != nil {
		fmt.Printf("⚠️  Error stopping order manager: %v\n", err)
	} else {
		fmt.Printf("✅ Order manager stopped\n")
	}
	
	err = marketHandler.Stop()
	if err != nil {
		fmt.Printf("⚠️  Error stopping market handler: %v\n", err)
	} else {
		fmt.Printf("✅ Market handler stopped\n")
	}

	// Test Summary
	fmt.Printf("\n📉 SHORT GRID STRATEGY TEST COMPLETED\n")
	fmt.Printf("=====================================\n")
	
	testsPassed := 0
	totalTests := 5
	
	if result != nil && sellHeavy && concentratedSelling {
		fmt.Printf("✅ Short grid calculation: PASSED\n")
		testsPassed++
	} else {
		fmt.Printf("❌ Short grid calculation: FAILED\n")
	}
	
	if marketHandler.IsRunning() || updateCount > 0 {
		fmt.Printf("✅ Component integration: PASSED\n")
		testsPassed++
	} else {
		fmt.Printf("❌ Component integration: FAILED\n")
	}
	
	if updateCount > 0 {
		fmt.Printf("✅ Market volatility analysis: PASSED (%d updates)\n", updateCount)
		testsPassed++
	} else {
		fmt.Printf("❌ Market volatility analysis: FAILED\n")
	}
	
	if gridStrategy.GetStatus() != "" {
		fmt.Printf("✅ Short strategy analysis: PASSED\n")
		testsPassed++
	} else {
		fmt.Printf("❌ Short strategy analysis: FAILED\n")
	}

	if orderStats.TotalOrders >= 0 {
		fmt.Printf("✅ Performance metrics: PASSED\n")
		testsPassed++
	} else {
		fmt.Printf("❌ Performance metrics: FAILED\n")
	}
	
	fmt.Printf("\n📊 TEST RESULTS: %d/%d PASSED\n", testsPassed, totalTests)
	
	if testsPassed == totalTests {
		fmt.Printf("🎉 ALL TESTS PASSED!\n")
	} else {
		fmt.Printf("⚠️  SOME TESTS FAILED\n")
	}
	
	fmt.Printf("\n📉 SHORT STRATEGY CHARACTERISTICS VERIFIED:\n")
	fmt.Printf("   - Sell-heavy order distribution\n")
	fmt.Printf("   - More sell orders in upper price regions\n")
	fmt.Printf("   - Optimized for downward trending markets\n")
	fmt.Printf("   - Sells on rallies, covers on dips\n")
}
