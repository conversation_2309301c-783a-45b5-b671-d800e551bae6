package futures

import (
	"testing"

	"github.com/stretchr/testify/suite"
)

type accountServiceTestSuite struct {
	baseTestSuite
}

func TestAccountService(t *testing.T) {
	suite.Run(t, new(accountServiceTestSuite))
}

func (s *accountServiceTestSuite) TestGetBalance() {
	data := []byte(`[
		{
			"accountAlias": "SgsR",
			"asset": "USDT",
			"balance": "122607.********",
			"crossWalletBalance": "23.********",
			"crossUnPnl": "0.********",
			"availableBalance": "23.********",
			"maxWithdrawAmount": "23.********",
		    "marginAvailable": true,
            "updateTime": *************
		}
	]`)
	s.mockDo(data, nil)
	defer s.assertDo()
	s.assertReq(func(r *request) {
		e := newSignedRequest()
		s.assertRequestEqual(e, r)
	})

	res, err := s.client.NewGetBalanceService().Do(newContext())
	s.r().NoError(err)
	s.r().Len(res, 1)
	e := &Balance{
		AccountAlias:       "SgsR",
		Asset:              "USDT",
		Balance:            "122607.********",
		CrossWalletBalance: "23.********",
		CrossUnPnl:         "0.********",
		AvailableBalance:   "23.********",
		MaxWithdrawAmount:  "23.********",
		MarginAvailable:    true,
		UpdateTime:         *************,
	}
	s.assertBalanceEqual(e, res[0])
}

func (s *accountServiceTestSuite) assertBalanceEqual(e, a *Balance) {
	r := s.r()
	r.Equal(e.AccountAlias, a.AccountAlias, "AccountAlias")
	r.Equal(e.Asset, a.Asset, "Asset")
	r.Equal(e.Balance, a.Balance, "Balance")
	r.Equal(e.CrossWalletBalance, a.CrossWalletBalance, "CrossWalletBalance")
	r.Equal(e.CrossUnPnl, a.CrossUnPnl, "CrossUnPnl")
	r.Equal(e.AvailableBalance, a.AvailableBalance, "AvailableBalance")
	r.Equal(e.MaxWithdrawAmount, a.MaxWithdrawAmount, "MaxWithdrawAmount")
}

func (s *accountServiceTestSuite) TestGetAccount() {
	data := []byte(`{
		"assets": [
			{
				"asset": "USDT",
				"initialMargin": "0.********",
				"maintMargin": "0.********",
				"marginBalance": "8.********",
				"maxWithdrawAmount": "8.********",
				"openOrderInitialMargin": "0.********",
				"positionInitialMargin": "0.********",
				"unrealizedProfit": "-0.********",
				"walletBalance": "9.********",
				"crossWalletBalance": "23.********",
				"crossUnPnl": "0.********",
				"availableBalance": "126.********",
				"marginAvailable": true,
				"updateTime": *************
			}
		 ],
		 "canDeposit": true,
		 "canTrade": true,
		 "canWithdraw": true,
		 "feeTier": 2,
		 "maxWithdrawAmount": "8.********",
		 "multiAssetsMargin": false,
		 "positions": [
			 {
				"isolated": false, 
				"leverage": "20",
				"initialMargin": "0.33683",
				"maintMargin": "0.02695",
				"openOrderInitialMargin": "0.00000",
				"positionInitialMargin": "0.33683",
				"symbol": "BTCUSDT",
				"unrealizedProfit": "-0.********",
				"entryPrice": "8950.5",
				"maxNotional": "250000",
				"positionSide": "BOTH",
				"positionAmt": "0.436",
				"bidNotional": "0",
				"askNotional": "0",
				"updateTime":*************
			 }
		 ],
		 "totalInitialMargin": "0.********",
		 "totalMaintMargin": "0.********",
		 "totalMarginBalance": "8.********",
		 "totalOpenOrderInitialMargin": "0.********",
		 "totalPositionInitialMargin": "0.********",
		 "totalUnrealizedProfit": "-0.********",
		 "totalWalletBalance": "9.********",
		 "updateTime": 0
	}`)
	s.mockDo(data, nil)
	defer s.assertDo()
	s.assertReq(func(r *request) {
		e := newSignedRequest()
		s.assertRequestEqual(e, r)
	})

	res, err := s.client.NewGetAccountService().Do(newContext())
	s.r().NoError(err)
	e := &Account{
		Assets: []*AccountAsset{
			{
				Asset:                  "USDT",
				InitialMargin:          "0.********",
				MaintMargin:            "0.********",
				MarginBalance:          "8.********",
				MaxWithdrawAmount:      "8.********",
				OpenOrderInitialMargin: "0.********",
				PositionInitialMargin:  "0.********",
				UnrealizedProfit:       "-0.********",
				WalletBalance:          "9.********",
				CrossWalletBalance:     "23.********",
				CrossUnPnl:             "0.********",
				AvailableBalance:       "126.********",
				MarginAvailable:        true,
				UpdateTime:             *************,
			},
		},
		CanTrade:          true,
		CanWithdraw:       true,
		CanDeposit:        true,
		FeeTier:           2,
		MaxWithdrawAmount: "8.********",
		MultiAssetsMargin: false,
		Positions: []*AccountPosition{
			{
				Isolated:               false,
				Leverage:               "20",
				InitialMargin:          "0.33683",
				MaintMargin:            "0.02695",
				OpenOrderInitialMargin: "0.00000",
				PositionInitialMargin:  "0.33683",
				Symbol:                 "BTCUSDT",
				UnrealizedProfit:       "-0.********",
				EntryPrice:             "8950.5",
				MaxNotional:            "250000",
				PositionSide:           "BOTH",
				PositionAmt:            "0.436",
				BidNotional:            "0",
				AskNotional:            "0",
				UpdateTime:             *************,
			},
		},
		TotalInitialMargin:          "0.********",
		TotalMaintMargin:            "0.********",
		TotalMarginBalance:          "8.********",
		TotalOpenOrderInitialMargin: "0.********",
		TotalPositionInitialMargin:  "0.********",
		TotalUnrealizedProfit:       "-0.********",
		TotalWalletBalance:          "9.********",
		UpdateTime:                  0,
	}
	s.assertAccountEqual(e, res)
}

func (s *accountServiceTestSuite) assertAccountEqual(e, a *Account) {
	r := s.r()
	r.Equal(e.CanDeposit, a.CanDeposit, "CanDeposit")
	r.Equal(e.CanTrade, a.CanTrade, "CanTrade")
	r.Equal(e.CanWithdraw, a.CanWithdraw, "CanWithdraw")
	r.Equal(e.FeeTier, a.FeeTier, "FeeTier")
	r.Equal(e.MaxWithdrawAmount, a.MaxWithdrawAmount, "MaxWithdrawAmount")
	r.Equal(e.TotalInitialMargin, a.TotalInitialMargin, "TotalInitialMargin")
	r.Equal(e.TotalMaintMargin, a.TotalMaintMargin, "TotalMaintMargin")
	r.Equal(e.TotalMarginBalance, a.TotalMarginBalance, "TotalMarginBalance")
	r.Equal(e.TotalOpenOrderInitialMargin, a.TotalOpenOrderInitialMargin, "TotalOpenOrderInitialMargin")
	r.Equal(e.TotalPositionInitialMargin, a.TotalPositionInitialMargin, "TotalPositionInitialMargin")
	r.Equal(e.TotalUnrealizedProfit, a.TotalUnrealizedProfit, "TotalUnrealizedProfit")
	r.Equal(e.TotalWalletBalance, a.TotalWalletBalance, "TotalWalletBalance")
	r.Equal(e.UpdateTime, a.UpdateTime, "UpdateTime")
	r.Equal(e.MultiAssetsMargin, a.MultiAssetsMargin, "MultiAssetsMargin")

	r.Len(a.Assets, len(e.Assets))
	for i := 0; i < len(a.Assets); i++ {
		r.Equal(e.Assets[i].Asset, a.Assets[i].Asset, "Asset")
		r.Equal(e.Assets[i].InitialMargin, a.Assets[i].InitialMargin, "InitialMargin")
		r.Equal(e.Assets[i].MaintMargin, a.Assets[i].MaintMargin, "MaintMargin")
		r.Equal(e.Assets[i].MarginBalance, a.Assets[i].MarginBalance, "MarginBalance")
		r.Equal(e.Assets[i].MaxWithdrawAmount, a.Assets[i].MaxWithdrawAmount, "MaxWithdrawAmount")
		r.Equal(e.Assets[i].OpenOrderInitialMargin, a.Assets[i].OpenOrderInitialMargin, "OpenOrderInitialMargin")
		r.Equal(e.Assets[i].PositionInitialMargin, a.Assets[i].PositionInitialMargin, "PositionInitialMargin")
		r.Equal(e.Assets[i].UnrealizedProfit, a.Assets[i].UnrealizedProfit, "UnrealizedProfit")
		r.Equal(e.Assets[i].WalletBalance, a.Assets[i].WalletBalance, "WalletBalance")
		r.Equal(e.Assets[i].CrossWalletBalance, a.Assets[i].CrossWalletBalance, "CrossWalletBalance")
		r.Equal(e.Assets[i].CrossUnPnl, a.Assets[i].CrossUnPnl, "CrossUnPnl")
		r.Equal(e.Assets[i].AvailableBalance, a.Assets[i].AvailableBalance, "AvailableBalance")
		r.Equal(e.Assets[i].MarginAvailable, a.Assets[i].MarginAvailable, "MarginAvailable")
		r.Equal(e.Assets[i].UpdateTime, a.Assets[i].UpdateTime, "UpdateTime")
	}

	r.Len(a.Positions, len(e.Positions))
	for i := 0; i < len(a.Positions); i++ {
		r.Equal(e.Positions[i].Isolated, a.Positions[i].Isolated, "Isolated")
		r.Equal(e.Positions[i].Leverage, a.Positions[i].Leverage, "Leverage")
		r.Equal(e.Positions[i].InitialMargin, a.Positions[i].InitialMargin, "InitialMargin")
		r.Equal(e.Positions[i].MaintMargin, a.Positions[i].MaintMargin, "MaintMargin")
		r.Equal(e.Positions[i].OpenOrderInitialMargin, a.Positions[i].OpenOrderInitialMargin, "OpenOrderInitialMargin")
		r.Equal(e.Positions[i].PositionInitialMargin, a.Positions[i].PositionInitialMargin, "PositionInitialMargin")
		r.Equal(e.Positions[i].Symbol, a.Positions[i].Symbol, "Symbol")
		r.Equal(e.Positions[i].UnrealizedProfit, a.Positions[i].UnrealizedProfit, "UnrealizedProfit")
		r.Equal(e.Positions[i].EntryPrice, a.Positions[i].EntryPrice, "EntryPrice")
		r.Equal(e.Positions[i].MaxNotional, a.Positions[i].MaxNotional, "MaxNotional")
		r.Equal(e.Positions[i].PositionSide, a.Positions[i].PositionSide, "PositionSide")
		r.Equal(e.Positions[i].PositionAmt, a.Positions[i].PositionAmt, "PositionAmt")
		r.Equal(e.Positions[i].BidNotional, a.Positions[i].BidNotional, "BidNotional")
		r.Equal(e.Positions[i].AskNotional, a.Positions[i].AskNotional, "AskNotional")
		r.Equal(e.Positions[i].UpdateTime, a.Positions[i].UpdateTime, "UpdateTime")
	}
}

func (s *accountServiceTestSuite) TestGetAccountV3() {
	data := []byte(`{
		"totalInitialMargin": "0.********",
		"totalMaintMargin": "0.********",
		"totalWalletBalance": "126.********",
		"totalUnrealizedProfit": "0.********",
		"totalMarginBalance": "126.********",
		"totalPositionInitialMargin": "0.********",
		"totalOpenOrderInitialMargin": "0.********",
		"totalCrossWalletBalance": "126.********",
		"totalCrossUnPnl": "0.********",
		"availableBalance": "126.********",
		"maxWithdrawAmount": "126.********",
		"assets": [
			{
				"asset": "USDT",
				"walletBalance": "23.********",
				"unrealizedProfit": "0.********",
				"marginBalance": "23.********",
				"maintMargin": "0.********",
				"initialMargin": "0.********",
				"positionInitialMargin": "0.********",
				"openOrderInitialMargin": "0.********",
				"crossWalletBalance": "23.********",
				"crossUnPnl": "0.********",
				"availableBalance": "126.********",
				"maxWithdrawAmount": "23.********",
				"marginAvailable": true,
				"updateTime": *************
			},
			{
				"asset": "BUSD",
				"walletBalance": "103.********",
				"unrealizedProfit": "0.********",
				"marginBalance": "103.********",
				"maintMargin": "0.********",
				"initialMargin": "0.********",
				"positionInitialMargin": "0.********",
				"openOrderInitialMargin": "0.********",
				"crossWalletBalance": "103.********",
				"crossUnPnl": "0.********",
				"availableBalance": "126.********",
				"maxWithdrawAmount": "103.********",
				"marginAvailable": true,
				"updateTime": *************
			}
		],
		"positions": [
			{
				"symbol": "BTCUSDT",
				"positionSide": "BOTH",
				"positionAmt": "1.000",
				"unrealizedProfit": "0.********",
				"isolatedMargin": "0.********",
				"notional": "0",
				"isolatedWallet": "0",
				"initialMargin": "0",
				"maintMargin": "0",
				"updateTime": 0
			}
		]
	}`)
	s.mockDo(data, nil)
	defer s.assertDo()

	s.assertReq(func(r *request) {
		expected := newSignedRequest()
		s.assertRequestEqual(expected, r)
	})

	res, err := s.client.NewGetAccountV3Service().Do(newContext())
	s.r().NoError(err)

	expected := &AccountV3{
		TotalInitialMargin:          "0.********",
		TotalMaintMargin:            "0.********",
		TotalWalletBalance:          "126.********",
		TotalUnrealizedProfit:       "0.********",
		TotalMarginBalance:          "126.********",
		TotalPositionInitialMargin:  "0.********",
		TotalOpenOrderInitialMargin: "0.********",
		TotalCrossWalletBalance:     "126.********",
		TotalCrossUnPnl:             "0.********",
		AvailableBalance:            "126.********",
		MaxWithdrawAmount:           "126.********",
		Assets: []*AccountAssetV3{
			{
				Asset:                  "USDT",
				WalletBalance:          "23.********",
				UnrealizedProfit:       "0.********",
				MarginBalance:          "23.********",
				MaintMargin:            "0.********",
				InitialMargin:          "0.********",
				PositionInitialMargin:  "0.********",
				OpenOrderInitialMargin: "0.********",
				CrossWalletBalance:     "23.********",
				CrossUnPnl:             "0.********",
				AvailableBalance:       "126.********",
				MaxWithdrawAmount:      "23.********",
				MarginAvailable:        true,
				UpdateTime:             *************,
			},
			{
				Asset:                  "BUSD",
				WalletBalance:          "103.********",
				UnrealizedProfit:       "0.********",
				MarginBalance:          "103.********",
				MaintMargin:            "0.********",
				InitialMargin:          "0.********",
				PositionInitialMargin:  "0.********",
				OpenOrderInitialMargin: "0.********",
				CrossWalletBalance:     "103.********",
				CrossUnPnl:             "0.********",
				AvailableBalance:       "126.********",
				MaxWithdrawAmount:      "103.********",
				MarginAvailable:        true,
				UpdateTime:             *************,
			},
		},
		Positions: []*AccountPositionV3{
			{
				Symbol:           "BTCUSDT",
				PositionSide:     "BOTH",
				PositionAmt:      "1.000",
				UnrealizedProfit: "0.********",
				IsolatedMargin:   "0.********",
				Notional:         "0",
				IsolatedWallet:   "0",
				InitialMargin:    "0",
				MaintMargin:      "0",
				UpdateTime:       0,
			},
		},
	}

	s.assertAccountV3Equal(expected, res)
}

func (s *accountServiceTestSuite) assertAccountV3Equal(expected, actual *AccountV3) {
	r := s.r()
	r.Equal(expected.TotalInitialMargin, actual.TotalInitialMargin, "TotalInitialMargin")
	r.Equal(expected.TotalMaintMargin, actual.TotalMaintMargin, "TotalMaintMargin")
	r.Equal(expected.TotalWalletBalance, actual.TotalWalletBalance, "TotalWalletBalance")
	r.Equal(expected.TotalUnrealizedProfit, actual.TotalUnrealizedProfit, "TotalUnrealizedProfit")
	r.Equal(expected.TotalMarginBalance, actual.TotalMarginBalance, "TotalMarginBalance")
	r.Equal(expected.TotalPositionInitialMargin, actual.TotalPositionInitialMargin, "TotalPositionInitialMargin")
	r.Equal(expected.TotalOpenOrderInitialMargin, actual.TotalOpenOrderInitialMargin, "TotalOpenOrderInitialMargin")
	r.Equal(expected.TotalCrossWalletBalance, actual.TotalCrossWalletBalance, "TotalCrossWalletBalance")
	r.Equal(expected.TotalCrossUnPnl, actual.TotalCrossUnPnl, "TotalCrossUnPnl")
	r.Equal(expected.AvailableBalance, actual.AvailableBalance, "AvailableBalance")
	r.Equal(expected.MaxWithdrawAmount, actual.MaxWithdrawAmount, "MaxWithdrawAmount")

	r.Len(actual.Assets, len(expected.Assets))
	for i := 0; i < len(expected.Assets); i++ {
		r.Equal(expected.Assets[i].Asset, actual.Assets[i].Asset, "Asset")
		r.Equal(expected.Assets[i].WalletBalance, actual.Assets[i].WalletBalance, "WalletBalance")
		r.Equal(expected.Assets[i].UnrealizedProfit, actual.Assets[i].UnrealizedProfit, "UnrealizedProfit")
		r.Equal(expected.Assets[i].MarginBalance, actual.Assets[i].MarginBalance, "MarginBalance")
		r.Equal(expected.Assets[i].MaintMargin, actual.Assets[i].MaintMargin, "MaintMargin")
		r.Equal(expected.Assets[i].InitialMargin, actual.Assets[i].InitialMargin, "InitialMargin")
		r.Equal(expected.Assets[i].PositionInitialMargin, actual.Assets[i].PositionInitialMargin, "PositionInitialMargin")
		r.Equal(expected.Assets[i].OpenOrderInitialMargin, actual.Assets[i].OpenOrderInitialMargin, "OpenOrderInitialMargin")
		r.Equal(expected.Assets[i].CrossWalletBalance, actual.Assets[i].CrossWalletBalance, "CrossWalletBalance")
		r.Equal(expected.Assets[i].CrossUnPnl, actual.Assets[i].CrossUnPnl, "CrossUnPnl")
		r.Equal(expected.Assets[i].AvailableBalance, actual.Assets[i].AvailableBalance, "AvailableBalance")
		r.Equal(expected.Assets[i].MaxWithdrawAmount, actual.Assets[i].MaxWithdrawAmount, "MaxWithdrawAmount")
		r.Equal(expected.Assets[i].MarginAvailable, actual.Assets[i].MarginAvailable, "MarginAvailable")
		r.Equal(expected.Assets[i].UpdateTime, actual.Assets[i].UpdateTime, "UpdateTime")
	}

	r.Len(actual.Positions, len(expected.Positions))
	for i := 0; i < len(expected.Positions); i++ {
		r.Equal(expected.Positions[i].Symbol, actual.Positions[i].Symbol, "Symbol")
		r.Equal(expected.Positions[i].PositionSide, actual.Positions[i].PositionSide, "PositionSide")
		r.Equal(expected.Positions[i].PositionAmt, actual.Positions[i].PositionAmt, "PositionAmt")
		r.Equal(expected.Positions[i].UnrealizedProfit, actual.Positions[i].UnrealizedProfit, "UnrealizedProfit")
		r.Equal(expected.Positions[i].IsolatedMargin, actual.Positions[i].IsolatedMargin, "IsolatedMargin")
		r.Equal(expected.Positions[i].Notional, actual.Positions[i].Notional, "Notional")
		r.Equal(expected.Positions[i].IsolatedWallet, actual.Positions[i].IsolatedWallet, "IsolatedWallet")
		r.Equal(expected.Positions[i].InitialMargin, actual.Positions[i].InitialMargin, "InitialMargin")
		r.Equal(expected.Positions[i].MaintMargin, actual.Positions[i].MaintMargin, "MaintMargin")
		r.Equal(expected.Positions[i].UpdateTime, actual.Positions[i].UpdateTime, "UpdateTime")
	}
}
